<template>
  <div class="speech-recognition">
    <van-button
      :type="isRecording ? 'danger' : 'primary'"
      size="small"
      :icon="isRecording ? 'stop-circle-o' : 'microphone-o'"
      class="speech-btn"
      @click="toggleRecording"
      :loading="isProcessing"
    >
      {{ buttonText }}
    </van-button>
    <div v-if="isRecording" class="recording-indicator">
      <span>正在录音...</span>
      <div class="pulse-animation"></div>
    </div>
  </div>
</template>

<script>
import { speechToText } from '@/api/speech'

export default {
  name: 'SpeechRecognition',
  props: {
    // 当前文本值，用于追加识别结果
    value: {
      type: String,
      default: ''
    },
    // 语音识别的语言
    language: {
      type: String,
      default: 'zh-CN'
    }
  },
  data() {
    return {
      mediaRecorder: null,      // 媒体录制器
      audioChunks: [],          // 音频数据块
      isRecording: false,       // 是否正在录音
      isProcessing: false,      // 是否正在处理
      isSupported: false,       // 浏览器是否支持
      recordingTime: 0,         // 录音时长
      recordingTimer: null,     // 录音计时器
      maxRecordingTime: 60,     // 最大录音时长（秒）
      audioBlob: null           // 录音的音频Blob
    }
  },
  computed: {
    buttonText() {
      if (this.isProcessing && !this.isRecording) {
        return '处理中...';
      } else if (this.isRecording) {
        return '停止录音';
      } else {
        return '语音输入';
      }
    }
  },
  mounted() {
    this.checkBrowserSupport();
  },
  beforeDestroy() {
    this.stopRecording();
    this.clearTimer();
  },
  methods: {
    // 检查浏览器是否支持录音功能
    checkBrowserSupport() {
      if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        this.isSupported = true;
      } else {
        this.isSupported = false;
        console.warn('此浏览器不支持录音功能');
      }
    },

    // 切换录音状态
    toggleRecording() {
      if (!this.isSupported) {
        this.$toast.fail('您的浏览器不支持录音功能');
        return;
      }

      if (this.isRecording) {
        this.stopRecording();
      } else {
        this.startRecording();
      }
    },

    // 开始录音
    startRecording() {
      this.isProcessing = true;

      // 请求麦克风权限
      navigator.mediaDevices.getUserMedia({ audio: true })
        .then(stream => {
          this.audioChunks = [];
          this.recordingTime = 0;

          // 创建MediaRecorder实例
          this.mediaRecorder = new MediaRecorder(stream);

          // 收集音频数据
          this.mediaRecorder.ondataavailable = (event) => {
            if (event.data.size > 0) {
              this.audioChunks.push(event.data);
            }
          };

          // 录音结束时的处理
          this.mediaRecorder.onstop = () => {
            // 停止所有音轨
            stream.getTracks().forEach(track => track.stop());

            // 创建音频Blob
            this.audioBlob = new Blob(this.audioChunks, { type: 'audio/webm' });

            // 发送到服务器进行识别
            this.sendAudioToServer();
          };

          // 开始录音
          this.mediaRecorder.start();
          this.isRecording = true;
          this.isProcessing = false;
          this.$emit('start');

          // 开始计时
          this.startTimer();
        })
        .catch(error => {
          console.error('获取麦克风权限失败:', error);
          this.isProcessing = false;
          this.$toast.fail('无法访问麦克风，请检查权限设置');
        });
    },

    // 停止录音
    stopRecording() {
      if (this.mediaRecorder && this.isRecording) {
        this.clearTimer();
        this.isRecording = false;

        try {
          this.mediaRecorder.stop();
          this.$emit('stop');
        } catch (error) {
          console.error('停止录音失败:', error);
          this.isProcessing = false;
          this.$toast.fail('停止录音失败，请重试');
        }
      }
    },

    // 开始计时器
    startTimer() {
      this.clearTimer();
      this.recordingTimer = setInterval(() => {
        this.recordingTime++;

        // 如果超过最大录音时间，自动停止
        if (this.recordingTime >= this.maxRecordingTime) {
          this.stopRecording();
          this.$toast('已达到最大录音时长');
        }
      }, 1000);
    },

    // 清除计时器
    clearTimer() {
      if (this.recordingTimer) {
        clearInterval(this.recordingTimer);
        this.recordingTimer = null;
      }
    },

    // 发送音频到服务器进行识别
    sendAudioToServer() {
      if (!this.audioBlob) {
        this.isProcessing = false;
        this.$toast.fail('录音失败，请重试');
        return;
      }

      this.isProcessing = true;

      // 创建FormData对象
      const formData = new FormData();
      formData.append('audio', this.audioBlob, 'recording.webm');

      // 调用语音识别接口
      speechToText(formData)
        .then(response => {
          this.isProcessing = false;

          if (response.code === 200 && response.data) {
            // 识别成功，更新文本
            const recognizedText = response.data.text || '';
            if (recognizedText) {
              this.$emit('input', this.value + recognizedText);
              this.$emit('result', recognizedText);
              this.$toast.success('语音识别成功');
            } else {
              this.$toast('未能识别到语音内容，请重试');
            }
          } else {
            this.$toast.fail(response.msg || '语音识别失败，请重试');
          }
        })
        .catch(error => {
          console.error('语音识别请求失败:', error);
          this.isProcessing = false;
          this.$toast.fail('语音识别服务异常，请重试');
        });
    }
  }
}
</script>

<style lang="scss" scoped>
.speech-recognition {
  display: inline-flex;
  align-items: center;

  .speech-btn {
    border-radius: 4px;
  }

  .recording-indicator {
    display: flex;
    align-items: center;
    margin-left: 8px;
    color: #f44;
    font-size: 12px;

    .pulse-animation {
      width: 8px;
      height: 8px;
      background-color: #f44;
      border-radius: 50%;
      margin-left: 5px;
      animation: pulse 1.5s infinite;
    }
  }
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(0.8);
    opacity: 0.8;
  }
}
</style>
