<template>
  <div class="speech-recognition">
    <van-button
      :type="isRecording ? 'danger' : 'primary'"
      size="small"
      :icon="isRecording ? 'stop-circle-o' : 'microphone-o'"
      class="speech-btn"
      @click="toggleRecording"
      :loading="isProcessing"
    >
      {{ buttonText }}
    </van-button>
    <div v-if="isRecording" class="recording-indicator">
      <span>正在录音...</span>
      <div class="pulse-animation"></div>
    </div>
  </div>
</template>

<script>
import { speechToText } from '@/api/speech'

export default {
  name: 'SpeechRecognition',
  props: {
    // 当前文本值，用于追加识别结果
    value: {
      type: String,
      default: ''
    },
    // 语音识别的语言
    language: {
      type: String,
      default: 'zh-CN'
    }
  },
  data() {
    return {
      mediaRecorder: null,      // 媒体录制器
      audioChunks: [],          // 音频数据块
      isRecording: false,       // 是否正在录音
      isProcessing: false,      // 是否正在处理
      isSupported: false,       // 浏览器是否支持
      recordingTime: 0,         // 录音时长
      recordingTimer: null,     // 录音计时器
      maxRecordingTime: 60,     // 最大录音时长（秒）
      audioBlob: null,          // 录音的音频Blob
      audioContext: null,       // 音频上下文
      audioBuffer: null,        // 音频缓冲区
      sampleRate: 16000         // 采样率
    }
  },
  computed: {
    buttonText() {
      if (this.isProcessing && !this.isRecording) {
        return '处理中...';
      } else if (this.isRecording) {
        return '停止录音';
      } else {
        return '语音输入';
      }
    }
  },
  mounted() {
    this.checkBrowserSupport();
  },
  beforeDestroy() {
    this.stopRecording();
    this.clearTimer();
  },
  methods: {
    // 检查浏览器是否支持录音功能
    checkBrowserSupport() {
      if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        this.isSupported = true;
      } else {
        this.isSupported = false;
        console.warn('此浏览器不支持录音功能');
      }
    },

    // 切换录音状态
    toggleRecording() {
      if (!this.isSupported) {
        this.$toast.fail('您的浏览器不支持录音功能');
        return;
      }

      if (this.isRecording) {
        this.stopRecording();
      } else {
        this.startRecording();
      }
    },

    // 开始录音
    startRecording() {
      this.isProcessing = true;

      // 请求麦克风权限
      navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: this.sampleRate,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true
        }
      })
        .then(stream => {
          this.audioChunks = [];
          this.recordingTime = 0;

          // 创建音频上下文
          try {
            const AudioContextClass = window.AudioContext || window.webkitAudioContext;
            if (AudioContextClass) {
              this.audioContext = new AudioContextClass({
                sampleRate: this.sampleRate
              });
            } else {
              console.warn('浏览器不支持AudioContext，将使用原始音频格式');
              this.audioContext = null;
            }
          } catch (error) {
            console.warn('创建AudioContext失败:', error);
            this.audioContext = null;
          }

          // 创建MediaRecorder实例，尝试使用wav格式
          let options = {};
          let mimeType = '';

          // 按优先级尝试不同的音频格式
          const supportedTypes = [
            'audio/wav',
            'audio/webm;codecs=opus',
            'audio/webm',
            'audio/mp4',
            'audio/ogg;codecs=opus'
          ];

          for (const type of supportedTypes) {
            if (MediaRecorder.isTypeSupported(type)) {
              options = { mimeType: type };
              mimeType = type;
              break;
            }
          }

          console.log('使用的音频格式:', mimeType || '默认格式');

          this.mediaRecorder = new MediaRecorder(stream, options);

          // 收集音频数据
          this.mediaRecorder.ondataavailable = (event) => {
            if (event.data.size > 0) {
              this.audioChunks.push(event.data);
            }
          };

          // 录音结束时的处理
          this.mediaRecorder.onstop = () => {
            // 停止所有音轨
            stream.getTracks().forEach(track => track.stop());

            // 创建原始音频Blob
            const originalBlob = new Blob(this.audioChunks, {
              type: this.mediaRecorder.mimeType || 'audio/webm'
            });

            // 检查是否需要转换格式
            const recordedType = this.mediaRecorder.mimeType || 'audio/webm';
            if (recordedType.includes('wav')) {
              // 如果已经是WAV格式，直接使用
              this.audioBlob = originalBlob;
              this.sendAudioToServer();
            } else {
              // 如果不是WAV格式，尝试转换
              this.convertToWav(originalBlob);
            }
          };

          // 开始录音
          this.mediaRecorder.start();
          this.isRecording = true;
          this.isProcessing = false;
          this.$emit('start');

          // 开始计时
          this.startTimer();
        })
        .catch(error => {
          console.error('获取麦克风权限失败:', error);
          this.isProcessing = false;
          this.$toast.fail('无法访问麦克风，请检查权限设置');
        });
    },

    // 停止录音
    stopRecording() {
      if (this.mediaRecorder && this.isRecording) {
        this.clearTimer();
        this.isRecording = false;

        try {
          this.mediaRecorder.stop();
          this.$emit('stop');
        } catch (error) {
          console.error('停止录音失败:', error);
          this.isProcessing = false;
          this.$toast.fail('停止录音失败，请重试');
        }
      }
    },

    // 开始计时器
    startTimer() {
      this.clearTimer();
      this.recordingTimer = setInterval(() => {
        this.recordingTime++;

        // 如果超过最大录音时间，自动停止
        if (this.recordingTime >= this.maxRecordingTime) {
          this.stopRecording();
          this.$toast('已达到最大录音时长');
        }
      }, 1000);
    },

    // 清除计时器
    clearTimer() {
      if (this.recordingTimer) {
        clearInterval(this.recordingTimer);
        this.recordingTimer = null;
      }
    },

    // 转换音频为WAV格式
    convertToWav(audioBlob) {
      // 如果浏览器不支持AudioContext，直接使用原始音频
      if (!this.audioContext) {
        console.warn('AudioContext不可用，使用原始音频格式');
        this.audioBlob = audioBlob;
        this.sendAudioToServer();
        return;
      }

      const reader = new FileReader();
      reader.onload = () => {
        this.audioContext.decodeAudioData(reader.result)
          .then(audioBuffer => {
            try {
              // 转换为WAV格式
              const wavBlob = this.audioBufferToWav(audioBuffer);
              this.audioBlob = wavBlob;
              console.log('音频转换为WAV格式成功');
            } catch (error) {
              console.error('WAV转换失败:', error);
              // 如果转换失败，使用原始音频
              this.audioBlob = audioBlob;
            }

            // 发送到服务器进行识别
            this.sendAudioToServer();
          })
          .catch(error => {
            console.error('音频解码失败:', error);
            // 如果转换失败，直接使用原始音频
            this.audioBlob = audioBlob;
            this.sendAudioToServer();
          });
      };

      reader.onerror = () => {
        console.error('文件读取失败');
        this.audioBlob = audioBlob;
        this.sendAudioToServer();
      };

      reader.readAsArrayBuffer(audioBlob);
    },

    // 将AudioBuffer转换为WAV格式的Blob
    audioBufferToWav(buffer) {
      const length = buffer.length;
      const sampleRate = buffer.sampleRate;
      const numberOfChannels = buffer.numberOfChannels;

      // 创建WAV文件头
      const arrayBuffer = new ArrayBuffer(44 + length * 2);
      const view = new DataView(arrayBuffer);

      // WAV文件头
      const writeString = (offset, string) => {
        for (let i = 0; i < string.length; i++) {
          view.setUint8(offset + i, string.charCodeAt(i));
        }
      };

      writeString(0, 'RIFF');
      view.setUint32(4, 36 + length * 2, true);
      writeString(8, 'WAVE');
      writeString(12, 'fmt ');
      view.setUint32(16, 16, true);
      view.setUint16(20, 1, true);
      view.setUint16(22, numberOfChannels, true);
      view.setUint32(24, sampleRate, true);
      view.setUint32(28, sampleRate * numberOfChannels * 2, true);
      view.setUint16(32, numberOfChannels * 2, true);
      view.setUint16(34, 16, true);
      writeString(36, 'data');
      view.setUint32(40, length * 2, true);

      // 写入音频数据
      const channelData = buffer.getChannelData(0);
      let offset = 44;
      for (let i = 0; i < length; i++) {
        const sample = Math.max(-1, Math.min(1, channelData[i]));
        view.setInt16(offset, sample < 0 ? sample * 0x8000 : sample * 0x7FFF, true);
        offset += 2;
      }

      return new Blob([arrayBuffer], { type: 'audio/wav' });
    },

    // 发送音频到服务器进行识别
    sendAudioToServer() {
      if (!this.audioBlob) {
        this.isProcessing = false;
        this.$toast.fail('录音失败，请重试');
        return;
      }

      this.isProcessing = true;

      // 创建FormData对象
      const formData = new FormData();

      // 根据音频类型确定文件名
      let fileName = 'recording.wav'; // 默认使用wav
      if (this.audioBlob.type.includes('wav')) {
        fileName = 'recording.wav';
      } else if (this.audioBlob.type.includes('webm')) {
        fileName = 'recording.webm';
      } else if (this.audioBlob.type.includes('mp4')) {
        fileName = 'recording.mp4';
      } else if (this.audioBlob.type.includes('ogg')) {
        fileName = 'recording.ogg';
      }

      console.log('发送音频文件:', fileName, '类型:', this.audioBlob.type);
      formData.append('file', this.audioBlob, fileName);

      // 调用语音识别接口
      speechToText(formData)
        .then(response => {
          this.isProcessing = false;

          if (response.code === 200 && response.data) {
            // 识别成功，更新文本
            const recognizedText = response.data.text || '';
            if (recognizedText) {
              this.$emit('input', this.value + recognizedText);
              this.$emit('result', recognizedText);
              this.$toast.success('语音识别成功');
            } else {
              this.$toast('未能识别到语音内容，请重试');
            }
          } else {
            this.$toast.fail(response.msg || '语音识别失败，请重试');
          }
        })
        .catch(error => {
          console.error('语音识别请求失败:', error);
          this.isProcessing = false;
          this.$toast.fail('语音识别服务异常，请重试');
        });
    }
  }
}
</script>

<style lang="scss" scoped>
.speech-recognition {
  display: inline-flex;
  align-items: center;

  .speech-btn {
    border-radius: 4px;
  }

  .recording-indicator {
    display: flex;
    align-items: center;
    margin-left: 8px;
    color: #f44;
    font-size: 12px;

    .pulse-animation {
      width: 8px;
      height: 8px;
      background-color: #f44;
      border-radius: 50%;
      margin-left: 5px;
      animation: pulse 1.5s infinite;
    }
  }
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(0.8);
    opacity: 0.8;
  }
}
</style>
