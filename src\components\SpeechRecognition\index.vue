<template>
  <div class="speech-recognition">
    <van-button 
      :type="isRecording ? 'danger' : 'primary'" 
      size="small" 
      :icon="isRecording ? 'stop-circle-o' : 'microphone-o'"
      class="speech-btn"
      @click="toggleRecording"
      :loading="isInitializing"
    >
      {{ isRecording ? '停止录音' : '语音输入' }}
    </van-button>
    <div v-if="isRecording" class="recording-indicator">
      <span>正在录音...</span>
      <div class="pulse-animation"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SpeechRecognition',
  props: {
    // Current text value to append to
    value: {
      type: String,
      default: ''
    },
    // Language for speech recognition
    language: {
      type: String,
      default: 'zh-CN'
    }
  },
  data() {
    return {
      recognition: null,
      isRecording: false,
      isInitializing: false,
      transcript: '',
      isSupported: false
    }
  },
  mounted() {
    this.checkBrowserSupport();
  },
  beforeDestroy() {
    this.stopRecognition();
  },
  methods: {
    checkBrowserSupport() {
      // Check if the browser supports the Web Speech API
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      if (!SpeechRecognition) {
        this.isSupported = false;
        console.warn('Speech recognition is not supported in this browser.');
        return;
      }
      
      this.isSupported = true;
      this.initRecognition();
    },
    
    initRecognition() {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      
      if (!SpeechRecognition) return;
      
      this.recognition = new SpeechRecognition();
      this.recognition.continuous = true;
      this.recognition.interimResults = true;
      this.recognition.lang = this.language;
      
      // Handle results
      this.recognition.onresult = (event) => {
        let finalTranscript = '';
        let interimTranscript = '';
        
        for (let i = event.resultIndex; i < event.results.length; i++) {
          const transcript = event.results[i][0].transcript;
          if (event.results[i].isFinal) {
            finalTranscript += transcript;
          } else {
            interimTranscript += transcript;
          }
        }
        
        if (finalTranscript) {
          // Emit the final transcript
          this.$emit('input', this.value + finalTranscript);
          this.$emit('result', finalTranscript);
        }
      };
      
      // Handle errors
      this.recognition.onerror = (event) => {
        console.error('Speech recognition error:', event.error);
        this.stopRecognition();
        this.$toast.fail('语音识别出错，请重试');
      };
      
      // Handle end of speech recognition
      this.recognition.onend = () => {
        this.isRecording = false;
        this.isInitializing = false;
      };
    },
    
    toggleRecording() {
      if (!this.isSupported) {
        this.$toast.fail('您的浏览器不支持语音识别功能');
        return;
      }
      
      if (this.isRecording) {
        this.stopRecognition();
      } else {
        this.startRecognition();
      }
    },
    
    startRecognition() {
      this.isInitializing = true;
      
      try {
        this.recognition.start();
        this.isRecording = true;
        this.$emit('start');
      } catch (error) {
        console.error('Failed to start speech recognition:', error);
        this.$toast.fail('启动语音识别失败，请重试');
      } finally {
        this.isInitializing = false;
      }
    },
    
    stopRecognition() {
      if (this.recognition && this.isRecording) {
        try {
          this.recognition.stop();
          this.$emit('stop');
        } catch (error) {
          console.error('Failed to stop speech recognition:', error);
        }
      }
      
      this.isRecording = false;
    }
  }
}
</script>

<style lang="scss" scoped>
.speech-recognition {
  display: inline-flex;
  align-items: center;
  
  .speech-btn {
    border-radius: 4px;
  }
  
  .recording-indicator {
    display: flex;
    align-items: center;
    margin-left: 8px;
    color: #f44;
    font-size: 12px;
    
    .pulse-animation {
      width: 8px;
      height: 8px;
      background-color: #f44;
      border-radius: 50%;
      margin-left: 5px;
      animation: pulse 1.5s infinite;
    }
  }
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(0.8);
    opacity: 0.8;
  }
}
</style>
