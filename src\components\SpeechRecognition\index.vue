<template>
  <div class="speech-recognition">
    <van-button
      :type="isRecording ? 'danger' : 'primary'"
      size="small"
      :icon="isRecording ? 'stop-circle-o' : 'microphone-o'"
      class="speech-btn"
      @click="toggleRecording"
      :loading="isConnecting"
      :disabled="!isSupported"
    >
      {{ buttonText }}
    </van-button>
    <div v-if="isRecording" class="recording-indicator">
      <span>{{ statusText }}</span>
      <div class="pulse-animation"></div>
    </div>
    <div v-if="interimResult" class="interim-result">
      <span class="interim-text">{{ interimResult }}</span>
    </div>
  </div>
</template>

<script>
import CryptoJS from 'crypto-js'

export default {
  name: 'SpeechRecognition',
  props: {
    // 当前文本值，用于追加识别结果
    value: {
      type: String,
      default: ''
    },
    // 讯飞语音识别配置
    config: {
      type: Object,
      default: () => ({
        // 这些参数需要根据实际的讯飞配置进行设置
        appId: 'c25b3295',
        apiKey: 'f0116c2b873eaa51a1c25bab8cad486d',
        apiSecret: 'ZTY1NzkyZjRkZjZkYjE2NzA1ODdiOWVj',
        // WebSocket地址
        hostUrl: 'wss://iat-api.xfyun.cn/v2/iat'
      })
    }
  },
  data() {
    return {
      // WebSocket相关
      websocket: null,
      isConnecting: false,
      isRecording: false,
      isSupported: false,

      // 录音相关
      audioContext: null,
      processor: null,
      microphone: null,

      // 识别结果
      interimResult: '',
      finalResult: '',

      // 状态管理
      statusText: '正在录音...',
      recordingTime: 0,
      recordingTimer: null,
      maxRecordingTime: 60,

      // 音频参数
      sampleRate: 16000,
      frameSize: 1280,

      // 讯飞IAT参数
      status: 0, // 音频的状态，0-首帧，1-中间帧，2-尾帧
      audioData: [],
      handlerInterval: null,

      // 识别参数
      iatWS: null,
      resultText: '',
      resultTextTemp: ''
    }
  },
  computed: {
    buttonText() {
      if (this.isConnecting) {
        return '连接中...';
      } else if (this.isRecording) {
        return '停止录音';
      } else {
        return '语音输入';
      }
    }
  },
  mounted() {
    this.checkBrowserSupport();
  },
  beforeDestroy() {
    this.stopRecording();
    this.closeWebSocket();
  },
  methods: {
    // 检查浏览器支持
    checkBrowserSupport() {
      if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        this.isSupported = true;
      } else {
        this.isSupported = false;
        console.warn('此浏览器不支持录音功能');
      }
    },

    // 切换录音状态
    toggleRecording() {
      if (!this.isSupported) {
        this.$toast.fail('您的浏览器不支持录音功能');
        return;
      }

      if (this.isRecording) {
        this.stopRecording();
      } else {
        this.startRecording();
      }
    },

    // 开始录音
    startRecording() {
      this.isConnecting = true;
      this.resultText = '';
      this.resultTextTemp = '';
      this.interimResult = '';

      // 连接WebSocket
      this.connectWebSocket();
    },

    // 停止录音
    stopRecording() {
      this.isRecording = false;
      this.isConnecting = false;
      this.statusText = '正在录音...';

      // 发送结束帧
      if (this.iatWS && this.iatWS.readyState === WebSocket.OPEN) {
        this.status = 2;
        this.iatWS.send(JSON.stringify({
          data: {
            status: this.status,
            format: "audio/L16;rate=16000",
            encoding: "raw",
            audio: ""
          }
        }));
      }

      // 停止录音
      this.stopAudioCapture();

      // 清理定时器
      if (this.handlerInterval) {
        clearInterval(this.handlerInterval);
        this.handlerInterval = null;
      }

      this.$emit('stop');
    },

    // 连接WebSocket
    connectWebSocket() {
      const url = this.getWebSocketUrl();

      this.iatWS = new WebSocket(url);

      this.iatWS.onopen = () => {
        console.log('WebSocket连接成功');
        this.isConnecting = false;
        this.startAudioCapture();
      };

      this.iatWS.onmessage = (event) => {
        this.handleWebSocketMessage(event);
      };

      this.iatWS.onerror = (error) => {
        console.error('WebSocket错误:', error);
        this.isConnecting = false;
        this.$toast.fail('连接语音识别服务失败');
      };

      this.iatWS.onclose = () => {
        console.log('WebSocket连接关闭');
        this.isConnecting = false;
        this.isRecording = false;
      };
    },

    // 生成WebSocket URL
    getWebSocketUrl() {
      const {hostUrl, apiKey, apiSecret, appId} = this.config;
      const url = new URL(hostUrl);
      const host = url.host;
      const path = url.pathname;

      const date = new Date().toUTCString();
      const algorithm = 'hmac-sha256';
      const headers = 'host date request-line';
      const signatureOrigin = `host: ${host}\ndate: ${date}\nGET ${path} HTTP/1.1`;
      const signatureSha = CryptoJS.HmacSHA256(signatureOrigin, apiSecret);
      const signature = CryptoJS.enc.Base64.stringify(signatureSha);
      const authorizationOrigin = `app_id="${appId}", api_key="${apiKey}", algorithm="${algorithm}", headers="${headers}", signature="${signature}"`;
      const authorization = btoa(authorizationOrigin);

      return `${hostUrl}?authorization=${authorization}&date=${date}&host=${host}`;
    },

    // 处理WebSocket消息
    handleWebSocketMessage(event) {
      const data = JSON.parse(event.data);

      if (data.code !== 0) {
        console.error('识别错误:', data.message);
        this.$toast.fail(`识别错误: ${data.message}`);
        this.stopRecording();
        return;
      }

      if (data.data && data.data.result) {
        const result = data.data.result;
        let resultText = '';

        if (result.ws) {
          result.ws.forEach(ws => {
            ws.cw.forEach(cw => {
              resultText += cw.w;
            });
          });
        }

        if (data.data.status === 2) {
          // 最终结果
          this.resultText += resultText;
          this.finalResult = this.resultText;
          this.interimResult = '';

          // 发送最终结果
          if (this.finalResult) {
            this.$emit('input', this.value + this.finalResult);
            this.$emit('result', this.finalResult);
            this.$toast.success('语音识别完成');
          }

          this.closeWebSocket();
        } else {
          // 临时结果
          this.resultTextTemp = resultText;
          this.interimResult = this.resultText + this.resultTextTemp;
        }
      }
    },

    // 开始音频捕获
    startAudioCapture() {
      navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: this.sampleRate,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true
        }
      })
          .then(stream => {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)({
              sampleRate: this.sampleRate
            });

            this.microphone = this.audioContext.createMediaStreamSource(stream);

            // 创建ScriptProcessor
            if (this.audioContext.createScriptProcessor) {
              this.processor = this.audioContext.createScriptProcessor(4096, 1, 1);
              this.processor.onaudioprocess = (event) => {
                this.processAudioData(event.inputBuffer);
              };

              this.microphone.connect(this.processor);
              this.processor.connect(this.audioContext.destination);
            }

            this.isRecording = true;
            this.status = 0; // 首帧
            this.$emit('start');

            // 开始发送音频数据
            this.startSendingAudio();
          })
          .catch(error => {
            console.error('获取麦克风权限失败:', error);
            this.isConnecting = false;
            this.$toast.fail('无法访问麦克风，请检查权限设置');
          });
    },

    // 处理音频数据
    processAudioData(inputBuffer) {
      const inputData = inputBuffer.getChannelData(0);
      const outputData = new Int16Array(inputData.length);

      // 转换为16位PCM
      for (let i = 0; i < inputData.length; i++) {
        const sample = Math.max(-1, Math.min(1, inputData[i]));
        outputData[i] = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
      }

      this.audioData.push(...outputData);
    },

    // 开始发送音频数据
    startSendingAudio() {
      this.handlerInterval = setInterval(() => {
        if (this.audioData.length >= this.frameSize && this.iatWS && this.iatWS.readyState === WebSocket.OPEN) {
          const audioFrame = this.audioData.splice(0, this.frameSize);
          const audioBase64 = btoa(String.fromCharCode.apply(null, new Uint8Array(audioFrame.buffer)));

          this.iatWS.send(JSON.stringify({
            data: {
              status: this.status,
              format: "audio/L16;rate=16000",
              encoding: "raw",
              audio: audioBase64
            }
          }));

          // 首帧之后改为中间帧
          if (this.status === 0) {
            this.status = 1;
          }
        }
      }, 40); // 每40ms发送一次
    },

    // 停止音频捕获
    stopAudioCapture() {
      if (this.processor) {
        this.processor.disconnect();
        this.processor = null;
      }

      if (this.microphone) {
        this.microphone.disconnect();
        this.microphone = null;
      }

      if (this.audioContext) {
        this.audioContext.close();
        this.audioContext = null;
      }
    },

    // 关闭WebSocket
    closeWebSocket() {
      if (this.iatWS) {
        this.iatWS.close();
        this.iatWS = null;
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.speech-recognition {
  display: inline-flex;
  align-items: center;
  flex-direction: column;

  .speech-btn {
    border-radius: 4px;
  }

  .recording-indicator {
    display: flex;
    align-items: center;
    margin-top: 8px;
    color: #f44;
    font-size: 12px;

    .pulse-animation {
      width: 8px;
      height: 8px;
      background-color: #f44;
      border-radius: 50%;
      margin-left: 5px;
      animation: pulse 1.5s infinite;
    }
  }

  .interim-result {
    margin-top: 8px;
    padding: 8px;
    background-color: #f5f5f5;
    border-radius: 4px;
    max-width: 200px;

    .interim-text {
      font-size: 12px;
      color: #666;
      line-height: 1.4;
    }
  }
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(0.8);
    opacity: 0.8;
  }
}
</style>
