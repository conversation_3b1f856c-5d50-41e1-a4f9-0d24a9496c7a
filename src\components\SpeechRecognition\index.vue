<template>
  <div class="speech-recognition">
    <van-button
      :type="isRecording ? 'danger' : 'primary'"
      size="small"
      :icon="isRecording ? 'stop-circle-o' : 'microphone-o'"
      class="speech-btn"
      @click="toggleRecording"
      :loading="isProcessing"
      :disabled="!isSupported"
    >
      {{ buttonText }}
    </van-button>
    <div v-if="isRecording" class="recording-indicator">
      <span>正在录音... {{ recordingTime }}s</span>
      <div class="pulse-animation"></div>
    </div>
    <div v-if="isProcessing && !isRecording" class="processing-indicator">
      <span>正在识别语音...</span>
      <van-loading size="16px" />
    </div>
  </div>
</template>

<script>
import { speechToText } from '@/api/speech'

export default {
  name: 'SpeechRecognition',
  props: {
    // 当前文本值，用于追加识别结果
    value: {
      type: String,
      default: ''
    },
    // 录音格式配置
    audioFormat: {
      type: String,
      default: 'wav', // 支持 'wav' 或 'mp3'
      validator: value => ['wav', 'mp3'].includes(value)
    },
    // 最大录音时长（秒）
    maxDuration: {
      type: Number,
      default: 60
    }
  },
  data() {
    return {
      // 录音相关
      mediaRecorder: null,
      audioChunks: [],
      isRecording: false,
      isProcessing: false,
      isSupported: false,

      // 时间管理
      recordingTime: 0,
      recordingTimer: null,

      // 音频参数
      sampleRate: 16000,
      audioBlob: null
    }
  },
  computed: {
    buttonText() {
      if (this.isProcessing && !this.isRecording) {
        return '识别中...';
      } else if (this.isRecording) {
        return '停止录音';
      } else {
        return '语音输入';
      }
    }
  },
  mounted() {
    this.checkBrowserSupport();
  },
  beforeDestroy() {
    this.stopRecording();
    this.clearTimer();
  },
  methods: {
    // 检查浏览器支持
    checkBrowserSupport() {
      if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        this.isSupported = true;
        console.log('浏览器支持录音功能');
      } else {
        this.isSupported = false;
        console.warn('此浏览器不支持录音功能');
      }
    },

    // 切换录音状态
    toggleRecording() {
      if (!this.isSupported) {
        this.$toast.fail('您的浏览器不支持录音功能');
        return;
      }

      if (this.isRecording) {
        this.stopRecording();
      } else {
        this.startRecording();
      }
    },

    // 开始录音
    startRecording() {
      this.isProcessing = true;
      this.recordingTime = 0;
      this.audioChunks = [];

      // 请求麦克风权限
      navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: this.sampleRate,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true
        }
      })
      .then(stream => {
        // 根据格式选择MIME类型
        let mimeType = 'audio/webm';
        if (this.audioFormat === 'wav' && MediaRecorder.isTypeSupported('audio/wav')) {
          mimeType = 'audio/wav';
        } else if (this.audioFormat === 'mp3' && MediaRecorder.isTypeSupported('audio/mp3')) {
          mimeType = 'audio/mp3';
        } else if (MediaRecorder.isTypeSupported('audio/webm;codecs=opus')) {
          mimeType = 'audio/webm;codecs=opus';
        }

        console.log('使用录音格式:', mimeType);

        // 创建MediaRecorder
        this.mediaRecorder = new MediaRecorder(stream, { mimeType });

        // 收集音频数据
        this.mediaRecorder.ondataavailable = (event) => {
          if (event.data.size > 0) {
            this.audioChunks.push(event.data);
          }
        };

        // 录音结束处理
        this.mediaRecorder.onstop = () => {
          // 停止所有音轨
          stream.getTracks().forEach(track => track.stop());

          // 创建音频文件
          this.createAudioFile();
        };

        // 开始录音
        this.mediaRecorder.start();
        this.isRecording = true;
        this.isProcessing = false;
        this.$emit('start');

        // 开始计时
        this.startTimer();

        console.log('开始录音，格式:', mimeType);
      })
      .catch(error => {
        console.error('获取麦克风权限失败:', error);
        this.isProcessing = false;
        this.$toast.fail('无法访问麦克风，请检查权限设置');
      });
    },

    // 停止录音
    stopRecording() {
      if (this.mediaRecorder && this.isRecording) {
        this.clearTimer();
        this.isRecording = false;

        try {
          this.mediaRecorder.stop();
          this.$emit('stop');
        } catch (error) {
          console.error('停止录音失败:', error);
          this.isProcessing = false;
          this.$toast.fail('停止录音失败，请重试');
        }
      }
    },

    // 开始计时
    startTimer() {
      this.clearTimer();
      this.recordingTimer = setInterval(() => {
        this.recordingTime++;

        // 检查是否超过最大录音时长
        if (this.recordingTime >= this.maxDuration) {
          this.stopRecording();
          this.$toast(`已达到最大录音时长 ${this.maxDuration} 秒`);
        }
      }, 1000);
    },

    // 清除计时器
    clearTimer() {
      if (this.recordingTimer) {
        clearInterval(this.recordingTimer);
        this.recordingTimer = null;
      }
    },

    // 创建音频文件
    createAudioFile() {
      if (this.audioChunks.length === 0) {
        this.isProcessing = false;
        this.$toast.fail('录音失败，没有音频数据');
        return;
      }

      // 创建音频Blob
      const mimeType = this.mediaRecorder.mimeType || 'audio/webm';
      this.audioBlob = new Blob(this.audioChunks, { type: mimeType });

      console.log('创建音频文件:', {
        size: this.audioBlob.size,
        type: this.audioBlob.type,
        duration: this.recordingTime
      });

      // 如果需要转换格式，进行转换
      if (this.audioFormat === 'wav' && !mimeType.includes('wav')) {
        this.convertToWav();
      } else if (this.audioFormat === 'mp3' && !mimeType.includes('mp3')) {
        this.convertToMp3();
      } else {
        // 直接上传
        this.uploadAudioFile();
      }
    },

    // 转换为WAV格式
    convertToWav() {
      console.log('开始转换为WAV格式...');

      // 创建一个新的WAV格式的Blob
      const wavBlob = new Blob([this.audioBlob], { type: 'audio/wav' });
      this.audioBlob = wavBlob;

      this.uploadAudioFile();
    },

    // 转换为MP3格式
    convertToMp3() {
      console.log('开始转换为MP3格式...');

      // 创建一个新的MP3格式的Blob
      const mp3Blob = new Blob([this.audioBlob], { type: 'audio/mp3' });
      this.audioBlob = mp3Blob;

      this.uploadAudioFile();
    },

    // 上传音频文件到服务器
    uploadAudioFile() {
      if (!this.audioBlob) {
        this.isProcessing = false;
        this.$toast.fail('音频文件创建失败');
        return;
      }

      this.isProcessing = true;

      // 创建FormData
      const formData = new FormData();

      // 根据格式确定文件名
      let fileName = 'recording.wav';
      if (this.audioFormat === 'mp3') {
        fileName = 'recording.mp3';
      } else if (this.audioBlob.type.includes('webm')) {
        fileName = 'recording.webm';
      }

      // 创建File对象
      const audioFile = new File([this.audioBlob], fileName, {
        type: this.audioBlob.type,
        lastModified: Date.now()
      });

      formData.append('file', audioFile);

      console.log('上传音频文件:', {
        fileName,
        size: audioFile.size,
        type: audioFile.type
      });

      // 调用语音识别接口
      speechToText(formData)
        .then(response => {
          this.isProcessing = false;

          console.log('语音识别响应:', response);

          if (response.code === 200 || response.success) {
            // 获取识别结果
            const recognizedText = response.data?.text || response.data?.result || response.result || '';

            if (recognizedText) {
              // 发送识别结果
              this.$emit('input', this.value + recognizedText);
              this.$emit('result', recognizedText);
              this.$toast.success('语音识别成功');

              console.log('识别结果:', recognizedText);
            } else {
              this.$toast('未能识别到语音内容，请重试');
            }
          } else {
            const errorMsg = response.msg || response.message || '语音识别失败';
            this.$toast.fail(errorMsg);
            console.error('语音识别失败:', response);
          }
        })
        .catch(error => {
          console.error('语音识别请求失败:', error);
          this.isProcessing = false;
          this.$toast.fail('语音识别服务异常，请重试');
        });
    }
  }
}
</script>

<style lang="scss" scoped>
.speech-recognition {
  display: inline-flex;
  align-items: center;
  flex-direction: column;

  .speech-btn {
    border-radius: 4px;
  }

  .recording-indicator {
    display: flex;
    align-items: center;
    margin-top: 8px;
    color: #f44;
    font-size: 12px;

    .pulse-animation {
      width: 8px;
      height: 8px;
      background-color: #f44;
      border-radius: 50%;
      margin-left: 5px;
      animation: pulse 1.5s infinite;
    }
  }

  .processing-indicator {
    display: flex;
    align-items: center;
    margin-top: 8px;
    color: #1989fa;
    font-size: 12px;

    span {
      margin-right: 8px;
    }
  }
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(0.8);
    opacity: 0.8;
  }
}
</style>
