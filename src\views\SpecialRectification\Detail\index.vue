<!--专项整治详情页-->
<template>
  <div class="detail-page">
    <!-- 导航栏 -->
    <van-nav-bar
      title="专项整治"
      left-arrow
      fixed
      @click-left="onClickLeft"
    />

    <!-- 任务信息区域 -->
    <div class="content-container">
      <!-- 基本信息 -->
      <div class="info-section">
        <div class="info-item">
          <span class="label">任务编号</span>
          <span class="value">{{ taskInfo.taskCode || '' }}</span>
        </div>
        <div class="info-item">
          <span class="label">任务类型</span>
          <span class="value">{{ taskInfo.taskType || '' }}</span>
        </div>
        <div class="info-item">
          <span class="label">任务级别</span>
          <span class="value" :class="{urgent: taskInfo.taskLevel == '紧急'}">{{ taskInfo.taskLevel || '' }}</span>
        </div>
<!--        <div class="info-item">-->
<!--          <span class="label">问题类型</span>-->
<!--          <span class="value">{{ taskInfo.problemType || '' }}</span>-->
<!--        </div>-->
        <div class="info-item">
          <span class="label">开始时间</span>
          <span class="value">{{ taskInfo.startTime || '' }}</span>
        </div>
        <div class="info-item">
          <span class="label">结束时间</span>
          <span class="value">{{ taskInfo.endTime || '' }}</span>
        </div>
      </div>

      <!-- 标题部分 -->
      <div class="title-section">
        <h3 class="section-title">任务标题</h3>
        <div class="content-text">{{ taskInfo.title || '' }}</div>
      </div>

      <!-- 任务描述 -->
      <div class="description-section">
        <h3 class="section-title">任务描述</h3>
        <div class="content-text">{{ taskInfo.description || '' }}</div>
      </div>

      <!-- 备注 -->
      <div class="remark-section">
        <h3 class="section-title">备注</h3>
        <div class="content-text">{{ taskInfo.remark || '' }}</div>
      </div>
    </div>

    <!-- 底部按钮区域 -->
    <div class="footer-buttons">
      <van-button
        class="btn-publish"
        type="primary"
        color="#1989fa"
        block
        @click="onPublishDraft">发布案卷</van-button>
    </div>
  </div>
</template>

<script>
import {getSpecialTaskDetail} from '@/api/common';

export default {
  name: "SpecialRectificationDetail",
  data() {
    return {
      taskId: '',
      typeData: [
        {
          value: 2,
          label: '普查'
        },
        {
          value: 1,
          label: '整治'
        }
      ],
      levelData: [
        {
          value: 1,
          label: '普通'
        },
        {
          value: 2,
          label: '紧急'
        }
      ],
      taskInfo: {
        taskCode: '',
        taskType: '',
        taskLevel: '',
        problemType: '',
        startTime: '',
        endTime: '',
        title: '',
        description: '',
        remark: ''
      },
      taskType: ''
    }
  },
  created() {
    // 获取路由参数中的ID
    this.taskId = this.$route.query.id;
    if (this.taskId) {
      this.getTaskDetail();
    }
  },
  methods: {
    // 获取任务详情
    async getTaskDetail() {
      try {
        this.$toast.loading({
          message: '加载中...',
          forbidClick: true,
          duration: 0
        });

        // 调用API获取任务详情
        const res = await getSpecialTaskDetail(this.taskId)
        console.log(res,"getSpecialTaskDetail");
        if (res.code === 200) {
          this.taskInfo = {
            taskCode: res.data.taskNumber,
            taskType: this.getDictText('typeData',res.data.taskType),
            taskLevel: this.getDictText('levelData',res.data.taskLevel),
            problemType: '暂无数据',
            startTime: res.data.startTime,
            endTime: res.data.endTime,
            title: res.data.taskTitle,
            description: res.data.taskDesc,
            remark: res.data.remark
          }
          this.taskType = res.data.taskType;
        } else {
          this.$toast.fail(res.msg || '获取详情失败');
        }

        setTimeout(() => {
          this.$toast.clear();
        }, 500);
      } catch (error) {
        console.error('获取任务详情失败:', error);
        this.$toast.fail('获取详情失败');
      }
    },

    // 返回上一页
    onClickLeft() {
      this.$router.go(-1);
    },

    // 发布案卷
    onPublishDraft() {
      this.$router.push({
        name: this.taskType == 1?'SpecialRectificationSubmitZz':'SpecialRectificationSubmit',
        query: {
          id: this.taskId,
          taskType: this.taskType
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-top: 46px; // 导航栏高度
  padding-bottom: 60px; // 底部按钮高度
}

// 基本信息部分
.info-section {
  background-color: #fff;
  padding: 15px;
  margin-bottom: 15px;

  .info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    line-height: 24px;
    padding: 5px 0 5px 0;

    &:last-child {
      margin-bottom: 0;
    }

    .label {
      color: #333;
      font-size: 16px;
    }

    .value {
      color: #333;
      font-size: 14px;
      text-align: right;

      &.urgent {
        color: #ff4d4f;
        font-weight: 500;
      }
    }
  }
}

// 标题、描述等文字区域
.title-section,
.description-section,
.remark-section {
  background-color: #fff;
  padding: 15px;
  margin-bottom: 15px;

  .section-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin: 0 0 12px 0;
  }

  .content-text {
    font-size: 14px;
    color: #666;
    line-height: 1.5;
    word-break: break-all;
  }
}

// 底部按钮
.footer-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  padding: 10px 15px;
  background-color: #fff;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);

  .btn-draft {
    flex: 1;
    margin-right: 10px;
    height: 40px;
    border-radius: 4px;
  }

  .btn-publish {
    flex: 1;
    height: 40px;
    border-radius: 4px;
  }
}
</style>
