"use strict";
const path = require("path");
// const defaultSettings = require('./src/settings.js')

function resolve(dir) {
  return path.join(__dirname, dir);
}

// const name = defaultSettings.title || '金华火车站站前区域综合管理信息指挥平台' // 标题
const name = "监督指挥"; // 标题

// const port = process.env.port || process.env.npm_config_port || 80 // 端口
// vue.config.js 配置说明
//官方vue.config.js 参考文档 https://cli.vuejs.org/zh/config/#css-loaderoptions
// 这里只列一部分，具体配置参考文档
module.exports = {
  // 部署生产环境和开发环境下的URL。
  // 默认情况下，Vue CLI 会假设你的应用是被部署在一个域名的根路径上
  // 例如 https://www.ruoyi.vip/。如果应用被部署在一个子路径上，你就需要用这个选项指定这个子路径。例如，如果你的应用被部署在 https://www.ruoyi.vip/admin/，则设置 baseUrl 为 /admin/。
  publicPath: process.env.NODE_ENV === "production" ? "/jiandumobile" : "/",
  // 在npm run build 或 yarn build 时 ，生成文件的目录名称（要和baseUrl的生产环境路径一致）（默认dist）
  outputDir: "build",
  // 用于放置生成的静态资源 (js、css、img、fonts) 的；（项目打包之后，静态资源会放在这个文件夹下）
  assetsDir: "static",
  // 是否开启eslint保存检测，有效值：ture | false | 'error'
  lintOnSave: process.env.NODE_ENV === "development",
  // 如果你不需要生产环境的 source map，可以将其设置为 false 以加速生产环境构建。
  productionSourceMap: false,
  // webpack-dev-server 相关配置
  devServer: {
    host: "0.0.0.0",
    port: 8080,
    open: true,
    allowedHosts: ["localhost", ".localhost", "127.0.0.1", "all"],
    logLevel: 'debug', // 设置日志级别为debug，显示更多信息
    proxy: {
      [process.env.VUE_APP_BASE_API]: {
        target: "http://************",
        // target: "http://*************:9000",
        // target: "http://*************:9000", //亮亮
        // target: "http://*************:9000",
        changeOrigin: true,
        pathRewrite: {
          ["^" + process.env.VUE_APP_BASE_API]: process.env.VUE_APP_BASE_API,
        }
      },
    },
  },
  transpileDependencies: ["vant"],
  css: {
    loaderOptions: {
      scss: {
        // 如果需要配置 scss，可以在这里添加
        // additionalData: `@import "@/styles/variables.scss";`
      },
    },
  },
  configureWebpack: {
    name: name,
    resolve: {
      alias: {
        "@": resolve("src"),
      },
    },
  },
  chainWebpack: (config) => {
    config.module
      .rule("svg")
      .exclude.add(resolve("src/assets/icons/svg"))
      .end();

    config.module
      .rule("icons")
      .test(/\.svg$/)
      .include.add(resolve("src/assets/icons/svg"))
      .end()
      .use("svg-sprite-loader")
      .loader("svg-sprite-loader")
      .options({
        symbolId: "icon-[name]",
      });
  },
};
