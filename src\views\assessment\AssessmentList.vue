<template>
  <div class="assessment-list">
    <!-- 标题栏 -->
    <van-nav-bar title="路段考核" left-arrow @click-left="onClickLeft" fixed style="position: fixed" />

    <!-- Tab切换 -->
    <div class="tabs-container">
      <van-tabs v-model="activeTab" sticky offset-top="46px">
        <van-tab title="我的任务" name="1">
          <!-- 下拉刷新 -->
          <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
            <!-- 列表 -->
            <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
              <div class="task-item" v-for="item in taskList" :key="item.id">
                <div class="task-header" />
                <div class="task-icon">
                  <img class="task-icon-img" src="@/assets/images/icon-title.png" alt="任务图标" />
                  <div class="task-title">{{ item.assessName }}</div>
                </div>
                <van-divider class="w-full" />
                <div class="task-content">
                  <div class="task-info">
                    <div class="info-item">
                      <div class="label">【备检路段】</div>
                      <div class="value">{{ item.checkRoads || '无' }}</div>
                    </div>
                    <div class="info-item">
                      <div class="label">【人行道范围】</div>
                      <div class="value">{{ item.walkingRange || '无' }}</div>
                    </div>
                    <div class="info-item">
                      <div class="label">【完成时间】</div>
                      <div class="value">{{ item.timeEnd }}</div>
                    </div>
                    <div class="info-item">
                      <div class="label">【检查状态】</div>
                      <!-- 1是待检查 2是待审查 3是已审查 4是正在检查 -->
                      <div class="status">{{ getStatus(item.status) }}</div>
                    </div>
                  </div>
                </div>
                <van-divider class="w-full" />
                <div class="task-footer">
                  <div class="info-item">
                    <span class="label">发布时间：</span>
                    <span>{{ item.publishTime }}</span>
                  </div>
                  <van-button type="info" size="mini" plain @click="goToRecord(item)">开始任务</van-button>
                </div>
              </div>
            </van-list>
          </van-pull-refresh>
        </van-tab>
        <van-tab title="历史任务" name="2">
          <!-- 历史任务列表，结构同上 -->
          <van-pull-refresh v-model="historyRefreshing" @refresh="onHistoryRefresh">
            <!-- 列表 -->
            <van-list
              v-model="historyLoading"
              :finished="historyFinished"
              finished-text="没有更多了"
              @load="onHistoryLoad"
            >
              <div class="task-item" v-for="item in historyTaskList" :key="item.id">
                <div class="task-header" />
                <div class="task-icon">
                  <img class="task-icon-img" src="@/assets/images/icon-title.png" alt="任务图标" />
                  <div class="task-title">{{ item.assessName }}</div>
                </div>
                <van-divider class="w-full" />
                <div class="task-content">
                  <div class="task-info">
                    <div class="info-item">
                      <div class="label">【备检路段】</div>
                      <div class="value">{{ item.checkRoads || '无' }}</div>
                    </div>
                    <div class="info-item">
                      <div class="label">【人行道范围】</div>
                      <div class="value">{{ item.walkingRange || '无' }}</div>
                    </div>
                    <div class="info-item">
                      <div class="label">【完成时间】</div>
                      <div class="value">{{ item.timeEnd }}</div>
                    </div>
                    <div class="info-item">
                      <div class="label">【检查状态】</div>
                      <!-- 1是待检查 2是待审查 3是已审查 4是正在检查 -->
                      <div class="status">{{ getStatus(item.status) }}</div>
                    </div>
                  </div>
                </div>
                <van-divider class="w-full" />
                <div class="task-footer">
                  <div class="info-item">
                    <span class="label"></span>
                    <span>{{ item.publishTime }}发布</span>
                  </div>
                  <van-button type="info" size="mini" plain @click="getDetail(item)">查看详情</van-button>
                </div>
              </div>
            </van-list>
          </van-pull-refresh>
        </van-tab>
      </van-tabs>
    </div>
  </div>
</template>

<script>
import { getAssessList } from '@/api/assessment'

export default {
  name: 'AssessmentList',
  data() {
    return {
      activeTab: 1,
      loading: false,
      finished: false,
      refreshing: false,
      taskList: [],
      page: 1,
      pageSize: 5,
      total: 0,
      historyTaskList: [],
      historyRefreshing: false,
      historyLoading: false,
      historyFinished: false,
      historyPage: 1,
      historyTotal: 0,
      historyPageSize: 5,
    }
  },
  methods: {
    getDetail(item) {
      this.$router.push({ path: '/assessment/detail', query: { id: item.id } })
    },
    onHistoryRefresh() {
      this.historyRefreshing = false
      this.historyFinished = true
      this.historyTaskList = []
      this.historyPage = 1
      this.onHistoryLoad()
    },
    async onHistoryLoad() {
      try {
        const params = {
          page: this.historyPage,
          pageSize: this.historyPageSize,
          filter: 2,
        }
        const res = await getAssessList(params)
        console.log(res)
        if (res.code === 200) {
          const list =
            res.rows.map((item) => {
              return {
                ...item,
                assessName: item.assessName,
                checkRoads: item.roadAssessMinorVOS
                  .filter((item) => item.roadType === 1)
                  .map((item) => item.roadName)
                  .join(','),
                walkingRange: item.roadAssessMinorVOS
                  .filter((item) => item.roadType === 2)
                  .map((item) => item.roadName)
                  .join(','),
              }
            }) || []
          this.historyTaskList.push(...list)
          this.historyTotal = res.total || 0
          this.historyLoading = false
          // 数据全部加载完成
          if (list.length >= this.historyTotal) {
            console.log('数据全部加载完成')
            this.historyFinished = true
          } else {
            this.historyPage++
          }
        } else {
          this.historyLoading = false
          this.$toast.fail(res.msg || '加载失败')
        }
      } catch (error) {
        this.historyLoading = false
        this.$toast.fail('加载失败')
      }
    },
    /**
     * 1是待检查 2是待审查 3是已审查 4是正在检查
     * @param status
     */
    getStatus(status) {
      switch (status) {
        case 1:
          return '待检查'
        case 2:
          return '待审查'
        case 3:
          return '已审查'
        case 4:
          return '正在检查'
      }
    },
    onClickLeft() {
      this.$router.back()
    },
    async onLoad() {
      try {
        const params = {
          page: this.page,
          pageSize: this.pageSize,
          filter: 1,
        }
        const res = await getAssessList(params)
        console.log(res)
        if (res.code === 200) {
          const list =
            res.rows.map((item) => {
              return {
                ...item,
                assessName: item.assessName,
                checkRoads: item.roadAssessMinorVOS
                  .filter((item) => item.roadType === 1)
                  .map((item) => item.roadName)
                  .join(','),
                walkingRange: item.roadAssessMinorVOS
                  .filter((item) => item.roadType === 2)
                  .map((item) => item.roadName)
                  .join(','),
              }
            }) || []
          this.taskList.push(...list)
          this.total = res.total || 0
          this.loading = false
          // 数据全部加载完成
          if (list.length >= this.total) {
            this.finished = true
          } else {
            this.page++
          }
        } else {
          this.loading = false
          this.$toast.fail(res.msg || '加载失败')
        }
      } catch (error) {
        this.loading = false
        this.$toast.fail('加载失败')
      }
    },
    async onRefresh() {
      this.finished = true
      this.page = 1
      this.taskList = []
      this.onLoad()
      this.refreshing = false
    },
    startTask(task) {
      // 处理开始任务的逻辑
      this.$toast('开始任务：' + task.title)
    },
    goToRecord(item) {
      this.$router.push({ path: '/assessment/record', query: { id: item.id } })
    },
  },
}
</script>

<style lang="scss" scoped>
.w-full {
  width: 100%;
  margin: 6px 0;
}
.assessment-list {
  min-height: 100vh;
  background: #f7f8fa;
  padding-top: 46px;

  .record-btn {
    padding: 16px;
    background: #fff;
  }

  .tabs-container {
    background: #fff;
  }

  .task-item {
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    padding: 0;
    background: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

    .task-header {
      width: 100%;
      height: 10px;
      background: #f5f6f8;
    }
    .task-icon {
      padding-top: 8px;
      padding-left: 16px;
      padding-right: 16px;
      display: flex;
      align-items: center;
      .task-title {
        margin-left: 8px;
        font-size: 16px;
        font-weight: bold;
        color: #323233;
      }
    }

    .task-content {
      flex: 1;
      padding-left: 16px;
      padding-right: 16px;
      width: 100%;

      .task-info {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;

        .info-item {
          margin-bottom: 4px;
          font-size: 14px;
          color: #646566;

          .label {
            color: #969799;
          }

          .status {
            color: #ff976a;
          }
          .value {
            padding: 0 4px;
            color: #323233;
          }
        }
      }
    }
    .task-footer {
      padding-left: 16px;
      padding-right: 16px;
      padding-bottom: 8px;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .info-item {
        display: flex;
        align-items: center;
        color: #999999;
        font-size: 14px;
      }
    }

    .van-button {
      margin-left: 12px;
      margin-top: 4px;
    }
  }
}
.task-icon-img {
  width: 24px;
  height: 24px;
}
</style>
