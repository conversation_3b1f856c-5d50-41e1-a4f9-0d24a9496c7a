# 语音转文字组件 (基于讯飞语音识别)

这是一个基于讯飞语音识别IAT服务的Vue组件，参考了iat-js-demo的实现方式。

## 功能特点

- ✅ 基于讯飞语音识别WebSocket API
- ✅ 实时语音识别，支持临时结果显示
- ✅ 支持多种音频参数配置
- ✅ 完整的错误处理和状态管理
- ✅ 响应式设计，适配移动端
- ✅ 支持环境变量配置

## 使用方法

### 1. 配置讯飞语音识别

在项目根目录创建 `.env` 文件，添加讯飞开放平台的配置：

```env
VUE_APP_XUNFEI_APP_ID=your_app_id
VUE_APP_XUNFEI_API_KEY=your_api_key
VUE_APP_XUNFEI_API_SECRET=your_api_secret
```

### 2. 在组件中使用

```vue
<template>
  <div>
    <speech-recognition 
      v-model="inputText"
      :config="speechConfig"
      @start="onSpeechStart"
      @stop="onSpeechStop"
      @result="onSpeechResult"
    />
  </div>
</template>

<script>
import SpeechRecognition from '@/components/SpeechRecognition'
import { getSpeechConfig } from '@/config/speech'

export default {
  components: {
    SpeechRecognition
  },
  data() {
    return {
      inputText: '',
      speechConfig: getSpeechConfig()
    }
  },
  methods: {
    onSpeechStart() {
      console.log('开始语音识别')
    },
    onSpeechStop() {
      console.log('语音识别结束')
    },
    onSpeechResult(result) {
      console.log('识别结果:', result)
    }
  }
}
</script>
```

## Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| value | String | '' | 当前文本值，用于追加识别结果 |
| config | Object | - | 讯飞语音识别配置对象 |

### config 配置对象

```javascript
{
  appId: 'your_app_id',           // 讯飞开放平台应用ID
  apiKey: 'your_api_key',         // API Key
  apiSecret: 'your_api_secret',   // API Secret
  hostUrl: 'wss://iat-api.xfyun.cn/v2/iat'  // WebSocket地址
}
```

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| input | (value: string) | 识别结果更新时触发，用于v-model |
| start | - | 开始录音时触发 |
| stop | - | 停止录音时触发 |
| result | (result: string) | 获得最终识别结果时触发 |

## 技术实现

### 1. WebSocket连接

组件使用WebSocket连接到讯飞语音识别服务，支持：
- 自动生成鉴权URL
- 连接状态管理
- 错误重连机制

### 2. 音频处理

- 使用MediaDevices API获取麦克风权限
- 通过AudioContext处理音频数据
- 实时转换为16位PCM格式
- 按帧发送到服务器

### 3. 识别结果处理

- 支持临时结果实时显示
- 最终结果自动追加到文本
- 完整的错误处理机制

## 浏览器兼容性

- Chrome 66+
- Firefox 60+
- Safari 12+
- Edge 79+

## 注意事项

1. **HTTPS要求**: 语音识别功能需要在HTTPS环境下使用
2. **麦克风权限**: 用户需要授权麦克风访问权限
3. **网络要求**: 需要稳定的网络连接
4. **配置安全**: 不要在前端代码中硬编码API密钥

## 故障排除

### 1. 连接失败
- 检查网络连接
- 验证讯飞配置是否正确
- 确认是否在HTTPS环境下

### 2. 无法录音
- 检查麦克风权限
- 确认浏览器支持MediaDevices API
- 检查设备是否有可用的音频输入设备

### 3. 识别准确率低
- 确保在安静环境中使用
- 说话清晰，语速适中
- 检查麦克风质量

## 开发调试

组件提供了详细的控制台日志，可以通过浏览器开发者工具查看：
- WebSocket连接状态
- 音频数据发送情况
- 识别结果返回过程
- 错误信息详情

## 更新日志

### v1.0.0
- 初始版本
- 基于讯飞语音识别IAT服务
- 支持实时语音识别
- 完整的错误处理机制
