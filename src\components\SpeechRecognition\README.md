# 语音转文字组件 (基于音频文件上传)

这是一个基于音频文件上传的Vue语音识别组件，通过录制音频文件并上传到服务器进行语音识别。

## 功能特点

- ✅ 基于音频文件上传的语音识别
- ✅ 支持WAV和MP3音频格式
- ✅ 实时录音状态显示和时长计时
- ✅ 完整的错误处理和状态管理
- ✅ 响应式设计，适配移动端
- ✅ 简单易用的HTTP接口调用

## 使用方法

### 1. 确保后端接口可用

确保后端提供语音识别接口：`/system/file/SpeechRecognition`

接口要求：
- 方法：POST
- 参数：FormData格式，包含名为`file`的音频文件
- 返回：JSON格式，包含识别结果

### 2. 在组件中使用

```vue
<template>
  <div>
    <speech-recognition
      v-model="inputText"
      audio-format="wav"
      :max-duration="60"
      @start="onSpeechStart"
      @stop="onSpeechStop"
      @result="onSpeechResult"
    />
  </div>
</template>

<script>
import SpeechRecognition from '@/components/SpeechRecognition'

export default {
  components: {
    SpeechRecognition
  },
  data() {
    return {
      inputText: ''
    }
  },
  methods: {
    onSpeechStart() {
      console.log('开始录音')
    },
    onSpeechStop() {
      console.log('录音结束')
    },
    onSpeechResult(result) {
      console.log('识别结果:', result)
    }
  }
}
</script>
```

## Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| value | String | '' | 当前文本值，用于追加识别结果 |
| audioFormat | String | 'wav' | 音频格式，支持 'wav' 或 'mp3' |
| maxDuration | Number | 60 | 最大录音时长（秒） |

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| input | (value: string) | 识别结果更新时触发，用于v-model |
| start | - | 开始录音时触发 |
| stop | - | 停止录音时触发 |
| result | (result: string) | 获得最终识别结果时触发 |

## 技术实现

### 1. 音频录制

组件使用MediaRecorder API进行音频录制：
- 获取麦克风权限
- 支持多种音频格式（WAV、MP3、WebM）
- 实时录音状态和时长显示

### 2. 文件处理

- 将录制的音频数据转换为Blob对象
- 创建File对象并设置正确的MIME类型
- 支持音频格式转换（基础转换）

### 3. 接口调用

- 使用FormData上传音频文件
- 调用后端语音识别接口
- 处理识别结果并更新文本

## 浏览器兼容性

- Chrome 66+
- Firefox 60+
- Safari 12+
- Edge 79+

## 注意事项

1. **HTTPS要求**: 语音识别功能需要在HTTPS环境下使用
2. **麦克风权限**: 用户需要授权麦克风访问权限
3. **网络要求**: 需要稳定的网络连接
4. **配置安全**: 不要在前端代码中硬编码API密钥

## 故障排除

### 1. 连接失败
- 检查网络连接
- 验证讯飞配置是否正确
- 确认是否在HTTPS环境下

### 2. 无法录音
- 检查麦克风权限
- 确认浏览器支持MediaDevices API
- 检查设备是否有可用的音频输入设备

### 3. 识别准确率低
- 确保在安静环境中使用
- 说话清晰，语速适中
- 检查麦克风质量

### 4. WebSocket超时错误
如果遇到"server read msg timeout"错误：

1. **检查音频数据发送**：
   - 确保音频数据格式正确（16位PCM，16kHz采样率）
   - 检查音频帧大小是否合适（建议1280字节）
   - 确认发送频率（每40ms发送一次）

2. **检查首帧发送**：
   - 确保首帧（status=0）正确发送
   - 首帧应该包含空的音频数据用于初始化

3. **检查结束帧**：
   - 停止录音时必须发送结束帧（status=2）
   - 结束帧也应该包含空的音频数据

4. **调试步骤**：
   ```javascript
   // 在浏览器控制台查看以下信息：
   // 1. WebSocket连接状态
   // 2. 音频数据缓冲区大小
   // 3. 发送的音频帧信息
   // 4. 服务器返回的消息
   ```

## 开发调试

组件提供了详细的控制台日志，可以通过浏览器开发者工具查看：
- WebSocket连接状态
- 音频数据发送情况
- 识别结果返回过程
- 错误信息详情

## 更新日志

### v1.0.0
- 初始版本
- 基于讯飞语音识别IAT服务
- 支持实时语音识别
- 完整的错误处理机制
