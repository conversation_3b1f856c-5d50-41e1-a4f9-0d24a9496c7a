(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-8245c1f8"],{9644:function(t,s,i){},c259:function(t,s,i){"use strict";i("9644")},db21:function(t,s,i){t.exports=i.p+"static/img/empty-bird.42ec3062.png"},e65e:function(t,s,i){"use strict";i.r(s);var a=function(){var t=this,s=t._self._c;return s("div",{staticClass:"phrase-settings"},[s("van-nav-bar",{attrs:{title:"惯用语设置","left-arrow":"",fixed:""},on:{"click-left":t.onClickLeft}}),s("van-pull-refresh",{on:{refresh:t.onRefresh},model:{value:t.refreshing,callback:function(s){t.refreshing=s},expression:"refreshing"}},[0!==t.phraseList.length||t.isLoading?s("div",{staticClass:"phrase-list"},t._l(t.phraseList,(function(i,a){return s("div",{key:a,staticClass:"phrase-item"},[s("div",{staticClass:"phrase-header"},[s("div",{staticClass:"phrase-index"},[t._v(t._s(a+1)+".")]),s("div",{staticClass:"phrase-content"},[t._v(t._s(i.phrase))])]),s("div",{staticClass:"phrase-actions"},[s("div",{staticClass:"action-item",on:{click:function(s){return t.deletePhrase(a)}}},[s("van-icon",{staticClass:"action-icon",attrs:{name:"delete-o",color:"#428FFC"}}),s("span",{staticClass:"action-text"},[t._v("删除")])],1),s("div",{staticClass:"action-item",on:{click:function(s){return t.editPhrase(a)}}},[s("van-icon",{staticClass:"action-icon",attrs:{name:"edit",color:"#428FFC"}}),s("span",{staticClass:"action-text"},[t._v("编辑")])],1),s("div",{staticClass:"action-item",on:{click:function(s){return t.viewPhrase(a)}}},[s("van-icon",{staticClass:"action-icon",attrs:{name:"eye-o",color:"#428FFC"}}),s("span",{staticClass:"action-text"},[t._v("查看")])],1)])])})),0):s("div",{staticClass:"empty-state"},[s("img",{staticClass:"empty-image",attrs:{src:i("db21")}}),s("van-button",{staticClass:"add-btn",attrs:{plain:"",type:"info"},on:{click:t.showAddPopup}},[s("van-icon",{attrs:{name:"plus"}}),t._v(" 新增惯用语 ")],1)],1)]),t.phraseList.length>0?s("div",{staticClass:"bottom-btn"},[s("van-button",{staticStyle:{"border-radius":"5px"},attrs:{type:"info",block:""},on:{click:t.showAddPopup}},[t._v("新增")])],1):t._e(),s("van-popup",{staticClass:"popup",attrs:{round:""},model:{value:t.showPopup,callback:function(s){t.showPopup=s},expression:"showPopup"}},[s("div",{staticClass:"popup-title"},[t._v(t._s(t.isEdit?"编辑":"新增"))]),s("div",{staticClass:"popup-content"},[s("van-field",{attrs:{type:"textarea",placeholder:"请输入",rows:"4",autosize:""},model:{value:t.currentPhrase,callback:function(s){t.currentPhrase=s},expression:"currentPhrase"}})],1),s("div",{staticClass:"popup-buttons"},[s("van-button",{staticClass:"cancel-btn",attrs:{plain:""},on:{click:t.cancelEdit}},[t._v("取消")]),s("van-button",{staticClass:"confirm-btn",attrs:{type:"info",plain:"",loading:t.submitLoading},on:{click:t.confirmEdit}},[t._v("确定")])],1)]),s("van-popup",{staticClass:"popup",attrs:{round:""},model:{value:t.showViewPopup,callback:function(s){t.showViewPopup=s},expression:"showViewPopup"}},[s("div",{staticClass:"popup-title"},[t._v("查看惯用语")]),s("div",{staticClass:"popup-content"},[s("div",{staticClass:"view-content"},[t._v(t._s(t.currentPhrase))])]),s("div",{staticClass:"popup-buttons"},[s("van-button",{staticClass:"confirm-btn",attrs:{type:"info",plain:"",block:""},on:{click:function(s){t.showViewPopup=!1}}},[t._v("关闭")])],1)])],1)},e=[],n=i("2934"),o={name:"PhraseSettings",data(){return{phraseList:[],showPopup:!1,showViewPopup:!1,currentPhrase:"",currentIndex:-1,isEdit:!1,isLoading:!1,submitLoading:!1,refreshing:!1,queryParams:{pageNum:1,pageSize:20}}},mounted(){this.getList()},methods:{onClickLeft(){this.$router.go(-1)},async getList(){try{this.isLoading=!0,this.$toast.loading({message:"加载中...",forbidClick:!0,duration:0});const t=await Object(n["j"])(this.queryParams);200===t.code?this.phraseList=t.rows||[]:this.$toast.fail(t.msg||"获取列表失败"),this.$toast.clear()}catch(t){console.error("获取惯用语列表失败:",t),this.$toast.fail("获取列表失败")}finally{this.isLoading=!1,this.refreshing&&(this.refreshing=!1)}},async onRefresh(){await this.getList()},showAddPopup(){this.isEdit=!1,this.currentPhrase="",this.currentIndex=-1,this.showPopup=!0},editPhrase(t){this.isEdit=!0,this.currentIndex=t,this.currentPhrase=this.phraseList[t].phrase,this.showPopup=!0},viewPhrase(t){this.currentPhrase=this.phraseList[t].phrase,this.showViewPopup=!0},deletePhrase(t){this.$dialog.confirm({title:"提示",message:"确定要删除这条惯用语吗？"}).then(async()=>{try{const s=this.phraseList[t].id;this.$toast.loading({message:"删除中...",forbidClick:!0,duration:0});const i=await Object(n["d"])(s);200===i.code?(this.$toast.success("删除成功"),this.getList()):this.$toast.fail(i.msg||"删除失败")}catch(s){console.error("删除惯用语失败:",s),this.$toast.fail("删除失败")}}).catch(()=>{})},cancelEdit(){this.showPopup=!1,this.currentPhrase=""},async confirmEdit(){if(this.currentPhrase.trim())try{if(this.submitLoading=!0,this.isEdit){const t={id:this.phraseList[this.currentIndex].id,phrase:this.currentPhrase},s=await Object(n["t"])(t);200===s.code?(this.$toast.success("编辑成功"),this.showPopup=!1,this.currentPhrase="",this.getList()):this.$toast.fail(s.msg||"编辑失败")}else{const t={phrase:this.currentPhrase},s=await Object(n["c"])(t);200===s.code?(this.$toast.success("新增成功"),this.showPopup=!1,this.currentPhrase="",this.getList()):this.$toast.fail(s.msg||"新增失败")}}catch(t){console.error(this.isEdit?"编辑惯用语失败:":"新增惯用语失败:",t),this.$toast.fail(this.isEdit?"编辑失败":"新增失败")}finally{this.submitLoading=!1}else this.$toast("请输入惯用语内容")}}},c=o,r=(i("c259"),i("2877")),h=Object(r["a"])(c,a,e,!1,null,"22087ccf",null);s["default"]=h.exports}}]);