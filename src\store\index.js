import Vue from 'vue'
import Vuex from 'vuex'
import createPersistedState from 'vuex-persistedstate'

Vue.use(Vuex)

// 初始化各模块
const modules = {
  // ... 其他模块
}

// 创建 store
export default new Vuex.Store({
  modules,
  // 配置 vuex-persistedstate
  plugins: [
    createPersistedState({
      // 存储方式，默认是 localStorage
      storage: window.sessionStorage,

      // 存储的 key，默认是 vuex
      key: 'supervision-mobile',

      // 需要持久化的状态，默认是全部
      paths: [
        // 用户相关
        'user',
      ],

      // 自定义序列化方法，默认使用JSON
      // 如果状态中有 circular reference，可能需要自定义
      // serialize: JSON.stringify,
      // deserialize: JSON.parse
    })
  ],

  // 根状态
  state: {
    // 全局状态
    user: ''
  },

  // 根 mutations
  mutations: {
    $uStore(state, payload) {
      // 判断是否多层级调用，state中为对象存在的情况，诸如user.info.score = 1
      let nameArr = payload.name.split('.')
      let len = nameArr.length
      if (nameArr.length >= 2) {
        let obj = state[nameArr[0]]
        for (let i = 1; i < len - 1; i++) {
          obj = obj[nameArr[i]]
        }
        obj[nameArr[len - 1]] = payload.value
      } else {
        // 单层级变量，在state就是一个普通变量的情况
        state[payload.name] = payload.value
      }
    }
  },

  // 根 actions
  actions: {
    // 全局 actions
  },

  // 根 getters
  getters: {
    // 全局 getters
  }
})
