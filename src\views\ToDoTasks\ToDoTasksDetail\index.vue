<template>
  <div class="task-detail-page">
    <!-- 导航栏 -->
    <van-nav-bar title="待办任务" left-arrow fixed @click-left="onClickLeft" />

    <!-- 标签切换 -->
    <div class="tab-fixed">
      <tabSwitch
        v-model="activeTab"
        :tabs="tabOptions"
        @change="handleTabChange"
      />
    </div>

    <!-- 内容区域 -->
    <div class="detail-content" v-if="taskDetail">
      <!-- 基本信息 Tab -->
      <basic-information
        v-if="activeTab === '基本信息'"
        :task-detail="taskDetail"
        :is-agree.sync="query.isAgree"
        :opinion.sync="query.opinion"
        :file-str.sync="query.fileStr"
        :coordinates="coordinates"
        @upload="afterRead"
      />

      <!-- 流程跟踪 Tab -->
      <flow-tracing v-if="activeTab === '流程跟踪'" />
    </div>

    <!-- 底部按钮 -->
    <div class="bottom-btns">
      <van-button plain class="cancel-btn" @click="onCancel">取消</van-button>
      <van-button type="info" class="submit-btn" @click="onBack"
        >退回</van-button
      >
      <van-button type="info" class="submit-btn" @click="onSubmit"
        >提交</van-button
      >
    </div>
  </div>
</template>

<script>
import tabSwitch from "@/components/TabSwitch";
import BasicInformation from "./childPage/BasicInformation";
import FlowTracing from "./childPage/flowTracing";
import { addAjcl, getProblemDetail, turnDownAjcl } from "@/api/common";

export default {
  name: "ToDoTasksDetail",
  components: {
    tabSwitch,
    BasicInformation,
    FlowTracing,
  },
  data() {
    return {
      activeTab: "基本信息",
      tabOptions: [
        { label: "基本信息", value: "基本信息" },
        { label: "流程跟踪", value: "流程跟踪" },
      ],
      query: {
        isAgree: 1,
        opinion: "",
        fileStr: "",
      },
      taskDetail: null,
      isLoading: false,
      areaOptions: [],
      typeOptions: [],
      sourceOptions: [],
      coordinates: [],
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    async init() {
      await this.getDictsList();
      await this.getTaskDetail();
    },

    async getDictsList() {
      // 使用Promise.all来并行处理多个字典请求
      return Promise.all([
        // 问题类型
        new Promise((resolve) => {
          this.getDicts("zhcg_wtlx").then((response) => {
            this.typeOptions = response.data.map((item) => ({
              label: item.dictLabel,
              value: item.dictValue,
            }));
            resolve();
          });
        }),
        // 问题来源
        new Promise((resolve) => {
          this.getDicts("zhcg_wtly").then((response) => {
            this.sourceOptions = response.data.map((item) => ({
              label: item.dictLabel,
              value: item.dictValue,
            }));
            resolve();
          });
        }),
        // 区划
        new Promise((resolve) => {
          this.getDicts("county").then((response) => {
            this.areaOptions = response.data.map((item) => ({
              label: item.dictLabel,
              value: item.dictValue,
            }));
            resolve();
          });
        }),
      ]);
    },

    // 获取任务详情
    async getTaskDetail() {
      try {
        this.isLoading = true;

        // 显示加载提示
        this.$toast.loading({
          message: "加载中...",
          forbidClick: true,
          duration: 0,
        });

        // 接口请求
        const res = await getProblemDetail(this.$route.query.id);
        console.log("ddd", res);
        const item = res.data;
        this.taskDetail = {
          id: item.taskcode,
          source: this.getDictText("sourceOptions", item.source),
          type: this.getDictText("typeOptions", item.type1id),
          mainCategory: item.type2Name,
          subCategory: item.type3Name,
          labz: item.type4id,
          street: item.streetid,
          community: this.getDictText("areaOptions", item.areaid),
          address: item.address,
          urgent: item.urgent,
          description: item.eventdesc,
        };
        this.coordinates = [item.x84, item.y84];

        this.$toast.clear();
      } catch (error) {
        console.error("获取任务详情失败:", error);
        this.$toast.fail("获取详情失败");
      } finally {
        this.isLoading = false;
      }
    },

    // 标签切换
    handleTabChange(tab) {
      this.activeTab = tab;
    },

    // 返回上一页
    onClickLeft() {
      this.$router.go(-1);
    },

    // 上传图片后回调
    afterRead(file) {
      this.$toast("图片上传中...");
      // 模拟上传
      setTimeout(() => {
        this.$toast.success("上传成功");
      }, 1000);
    },

    // 取消按钮
    onCancel() {
      this.$dialog
        .confirm({
          title: "提示",
          message: "确定要取消处理吗？",
        })
        .then(() => {
          this.$router.go(-1);
        })
        .catch(() => {
          // 取消操作
        });
    },

    // 退回按钮
    onBack() {
      // 退回处理
      turnDownAjcl({
        eventType: this.$route.query.prestatus == 3 ? 3 : 14,
        id: this.$route.query.id,
      }).then((res) => {
        if (res.code == 200) {
          this.$toast.clear();
          this.$toast.success("退回成功");
          setTimeout(() => {
            this.$router.go(-1);
          }, 1000);
        }
      });
    },

    // 提交按钮
    onSubmit() {
      if (this.query.fileStr.length === 0) {
        return this.$toast("请至少上传一张图片");
      }
      if (!this.query.opinion.trim()) {
        this.$toast("请输入问题说明");
        return;
      }

      this.$toast.loading({
        message: "提交中...",
        forbidClick: true,
        duration: 0,
      });

      // 提交处理
      addAjcl({
        ...this.query,
        operateType: Number(this.$route.query.operateType),
        eventId: this.$route.query.id,
      }).then((res) => {
        if (res.code == 200) {
          this.$toast.clear();
          this.$toast.success("提交成功");
          setTimeout(() => {
            this.$router.go(-1);
          }, 1000);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.task-detail-page {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-top: 46px;
  padding-bottom: 60px;
}

// 标签栏固定定位
.tab-fixed {
  position: fixed;
  top: 46px;
  left: 0;
  right: 0;
  z-index: 99;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

// 内容区域
.detail-content {
  padding-top: 15px;
  margin-top: 44px; // 标签栏高度
}

// 底部按钮
.bottom-btns {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  padding: 10px 15px;
  background-color: #fff;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);

  .van-button {
    flex: 1;
    height: 40px;
    border-radius: 4px;
  }

  .submit-btn {
    margin-left: 10px;
  }
}
</style>
