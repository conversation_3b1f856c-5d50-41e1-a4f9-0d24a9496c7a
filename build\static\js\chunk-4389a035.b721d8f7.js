(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4389a035"],{"1f80":function(t,e,s){"use strict";s.r(e);var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"todo-page"},[e("van-nav-bar",{attrs:{title:"待办任务","left-arrow":"",fixed:""},on:{"click-left":t.onClickLeft}}),e("div",{staticClass:"tab-fixed"},[e("tabSwitch",{attrs:{tabs:t.tabOptions},on:{change:t.handleTabChange},model:{value:t.queryParams.prestatus,callback:function(e){t.$set(t.queryParams,"prestatus",e)},expression:"queryParams.prestatus"}})],1),e("van-pull-refresh",{on:{refresh:t.onRefresh},model:{value:t.refreshing,callback:function(e){t.refreshing=e},expression:"refreshing"}},[e("van-list",{attrs:{finished:t.finished,"finished-text":"没有更多了"},on:{load:t.onLoad},model:{value:t.loading,callback:function(e){t.loading=e},expression:"loading"}},[e("div",{staticClass:"todo-list"},t._l(t.List,(function(s,a){return e("van-cell-group",{key:a},[e("van-cell",{staticClass:"todo-item",on:{click:function(e){return t.getTaskDetail(s)}}},[e("div",{staticClass:"todo-content"},[e("div",{staticClass:"source-info"},[e("div",{staticClass:"source-info-left"},[e("div",{staticClass:"source-icon"},[e("van-icon",{attrs:{name:t.titleIcon,size:"30"}})],1),e("span",{staticClass:"source-label"},[t._v("问题来源：")]),e("span",{staticClass:"source-value"},[t._v(t._s(s.source))])])]),e("div",{staticClass:"todo-container"},[e("div",{staticClass:"todo-container-left"},[e("van-image",{attrs:{width:"68",height:"68",src:s.image,fit:"cover",radius:"5"}})],1),e("div",{staticClass:"todo-container-right"},[e("div",{staticClass:"todo-title"},[t._v(" "+t._s(s.title)+" ")]),e("div",{staticClass:"todo-info"},[e("span",{staticClass:"time"},[t._v(t._s(s.time))])]),e("div",{staticClass:"todo-title"},[t._v(" "+t._s(s.content)+" ")]),e("div",{staticClass:"countdown"},[e("span",{staticClass:"countdown-label"},[t._v("剩余："+t._s(s.countdown))]),s.urgent?[e("span",{staticClass:"urgent-tag"},[t._v("紧急")])]:t._e()],2)])])])])],1)})),1)])],1)],1)},i=[],n=(s("14d9"),s("e9f5"),s("ab43"),s("eeb8")),o=s("2934"),l={name:"index",components:{tabSwitch:n["a"]},data(){return{queryParams:{prestatus:"3",pageSize:10,pageNum:1},tabOptions:[{label:"待核查",value:"16"},{label:"待核实",value:"3"}],List:[],titleIcon:s("6f8e"),refreshing:!1,loading:!1,finished:!1,total:0,isLoading:!1,sourceOptions:[]}},mounted(){this.init()},methods:{async init(){await this.getDictsList(),await this.getList()},async getDictsList(){return new Promise(t=>{this.getDicts("zhcg_wtly").then(e=>{this.sourceOptions=e.data.map(t=>({label:t.dictLabel,value:t.dictValue})),t()})})},async getList(){if(!this.isLoading)try{this.isLoading=!0,this.$toast.loading({message:"加载中...",forbidClick:!0,duration:0});const t=await Object(o["l"])({...this.queryParams,dealPersonId:this.user.userId});this.total=t.total,this.refreshing&&(this.List=[]),this.List.push(...t.rows.map(t=>({id:t.id,source:this.getDictText("sourceOptions",t.source),title:t.type4id,time:t.createtime,countdown:t.remainTime,content:t.eventdesc,urgent:1==t.urgent,image:t.fileStr?this.$getImageUrl(t.fileStr):""}))),this.finished=this.List.length>=this.total,this.loading=!1,this.refreshing&&(this.refreshing=!1),this.$toast.clear()}catch(t){console.error("获取列表失败:",t),this.$toast.fail("获取列表失败"),this.$toast.clear()}finally{this.isLoading=!1}},getTaskDetail(t){console.log(1111);let e=null;e="3"==this.queryParams.prestatus?21:"16"==this.queryParams.prestatus?22:null,this.$router.push({name:"ToDoTasksDetail",query:{id:t.id,operateType:e}})},async onRefresh(){this.queryParams.pageNum=1,this.finished=!1,await this.getList()},onLoad(){this.queryParams.pageNum+=1,this.getList()},handleTabChange(t){this.queryParams.pageNum=1,this.List=[],this.finished=!1,this.getList()},onClickLeft(){this.$router.go(-1)}}},r=l,c=(s("afec"),s("2877")),u=Object(c["a"])(r,a,i,!1,null,"69f21a54",null);e["default"]=u.exports},3224:function(t,e,s){"use strict";s("f833")},"6f8e":function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADoAAAA6CAYAAADhu0ooAAAAAXNSR0IArs4c6QAABzNJREFUaEPlW11sXEcV/r6ZazupE+ykSVPqxFQuQZCHShVI/KQNJo5AIFW0EkiIh7ZSIaDSVq2wipQ+xHloRIOjRPwTqER5QEggUVQJhNQUQ1raSiCkPgSkQkRdp6RNG9s0Jrb3znx07mad9Xptz73rOLtwn+zdM2fPN+fMOXN+LhH7SNx5COs6DdZ6hzXoQDJbgm2zYCyLRuhKDkoAn1rMrvGYmfK48OwFTGGIPobvskLu2K/2qzvQZdegywomhulq0TjCW4O3Jk7i3J+PsbTU7y4BVOwfxtUw6G42gLWAgraNwfjIIN4EqHqA6wLtH1KSdOM6BRNtoUfCjD+P0yNDTGvFXgA0mOrWdehxBm0thHFOVBqkr05g7OQBzlbLPw9opsku9MojaUWQFZmtR2l2Cq9Ua7YKqLh7GL0kOloZZEV2Wkw/9QBeqZzZOaD9w9pkiY3/CyArGJxwbmSQb4T/M6Dv36u2znfj+tWKiau1mSH8vD6J0XBeM6ADR7UFDl2rJcBq/s7sDMZP7ONZYkim/x3oa/ZYWXRzglZHHsQ/uPNRrV+T4J1FGbXCuukU/+LHv6FrnEF3boGJLQ6uz4KbPPxGElcBxkr+LQAzAM+A9rQDTrV5uKX4lwysTV0fE/ZK2AD4TtKsD3wkP0uZs54aN7IvEcicS57Hekxw12FtawPWxi9Mezy5j8SeqDXiyyAepTfH69F7uv63neIBEtfG8cMJwj4M4bUo+uBxS5jm7kO6gRY2ZpEX1tP6JwBtjaGvppHwJSM7Mu8zgwHAfTcvL4BjvmRutRb/iVkbbkvcdUjbY8OKjL8X0H0xzGtpJJxxtLsrZhzMNZH7DYh3FeR3zMgejlkbLv0cOKz3xBAHGpn0CYDvi6VfANbbTxvgb+FzD7yXxv2qKC9Af6VPbotdnwuop3ueDM6i6MN7Kmc1nE0SPyjKScBZ4+3NseujgWamBncylnE9OokPG5lfZBpl+jmSBxrhl8LuWM6jV/hHAy07IvenRgSTdNAoefwi0DtJ7muIn7MfMEQIZ8s+0UAFbIJxzy7LcUkCfovefLt83os7tjktye6KDTPxQIlu0L3QINBhevPDi0C/CGiwEX66HBp1DleZNveXhgRrBdMta8G9CDSQmHs+SJhfZ5tFf6uo4QY2bobe3hi7Ptp0yw7E/T76qlZHAsHebTyeCV85ug8b4sexgtbSCRwz3gzErs8FVEx/BPKWWOYL6LzdOXcpJ7aI7g+FeRG/o7Nfjl2fD6jxA4AK3E2D3eMEZb9QLZin+050crAAEe+nN7+9LEDL5usfInV37A+U6ThGmc8vDAVpj4z5Sd4kQeJjRuZQHhlyabTCWMZ/AtTtErcT2lzHQaUCxgmeETCi1ByzBvPqrBVeFzOiOyV9BEQvkV0xa8utMxDOADgF8ueLpXxLAS8EtJZhCD00sKFUGqrlsbeVxQTLwIeyK5H6FLOx6dhlB5rHhK4U7Ypp1CRoh5CspEYDL3m4K6ZRBe8rfRZAH8olkNrqfnZGIYyS/KOceXwxc3Ye7Uz8XgL9gq5d7IwKPEvqJYi/zONtKxaUW6OFva73dwDJ6Xmmm8VS/9Om87qZt4W+WeScSXjKyH6lem1jF5BLSXyMPLk0Kuu+D+FjMYzr0bAqrWo47ZNOUMm8C8iKeV1v/HEWqABWxcy7rOxz2cXD4GbCPVZ000Kxzch+NHZ9Po02mL1QHITMk0E4wX8KRkdiBa1Dd3myl2YspfiSvSk29ERrVE1YYYDsBylMxFhFNFA0mlZl0ugoffK97K8VqBmhOu1bBm000GY03Vw1o9iWxMrUdbXfKPlZ5nXpP0PqkRizW4wmtq6btST2HFFf7BSKN+4ZAiEtK/RUN5qya2TRJD6YvhDaiB+KEUQOjnu+rl61xQ1OtWrvpQRcyNUI9nRfJbE3ZhcX0IgvpzSfrO6mWbmnixfbLhXDl5MnawTnae2Xa7v+ybyX8LIg9h56zGsGF280cUzO3Bab4GetfUjsP4Ibooc1QpiBewREVDUwXNXePlL7a5vAFS0oNIPlvwYqqk9aTg50cEEmtIhas2GNf+NUNn5zy0Ftbu/I1w4Ml3JPt92IG0S/mTTtIS8tzzCYKRLjSjXqEhs3wwDXB6EHUJbfVs8wGJhzDnrDwp6K7bXM4baYPP4AX8uAhkHHa7rQG63V5Q5Fk3wfwsrU3/HPMMv7/zUiV1aAuOcotrXajO5ixhPqTU8PYnTB0GNYEMZY2zuxrVVndSugwxRKOonRRcZYy2ThvF7Xja2xt6UmOY6XfI9Haew8Ti85mFyhDpo169DTarO7YUY3ncCrUaPmlzRUfnnAe2yInUO6UtoNsRIeE7lfHqgWOMzydu/ARuexvtnCTwDopjH55gwma021dtOXfe9lbsGQzM616Awv+EwbdCQO7SlgVkvbISa2t8FhBqmxmM5e8HkI58H6r3/UAv0v/818wFIvwRoAAAAASUVORK5CYII="},a05a:function(t,e,s){},afec:function(t,e,s){"use strict";s("a05a")},eeb8:function(t,e,s){"use strict";var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"tab-switch"},[e("van-tabs",{attrs:{border:!1,"line-width":20,"line-height":"2px",color:"#1989fa","title-active-color":"#1989fa","title-inactive-color":"#666"},on:{change:t.handleChange},model:{value:t.modelValue,callback:function(e){t.modelValue=e},expression:"modelValue"}},t._l(t.tabs,(function(t,s){return e("van-tab",{key:s,attrs:{title:t.label,name:t.value}})})),1)],1)},i=[],n={name:"TabSwitch",props:{tabs:{type:Array,default:()=>[]},value:{type:[String,Number],default:""}},computed:{modelValue:{get(){return this.value},set(t){this.$emit("input",t)}}},methods:{handleChange(t){this.$emit("change",t)}}},o=n,l=(s("3224"),s("2877")),r=Object(l["a"])(o,a,i,!1,null,"185e5f8f",null);e["a"]=r.exports},f833:function(t,e,s){}}]);