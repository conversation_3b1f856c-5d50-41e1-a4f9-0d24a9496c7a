(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0ab84e"],{"162e":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t._self._c;return e("div",[e("router-view")],1)},n=[],c={name:"LayoutIndex",components:{},props:{},data(){return{active:0}},computed:{},watch:{$route(t){const e=t.name;"Home"==e&&0!=this.active?this.active=0:"Map"==e&&1!=this.active?this.active=1:"Mine"==e&&2!=this.active&&(this.active=2)}},mounted(){},created(){},methods:{}},o=c,s=a("2877"),u=Object(s["a"])(o,i,n,!1,null,null,null);e["default"]=u.exports}}]);