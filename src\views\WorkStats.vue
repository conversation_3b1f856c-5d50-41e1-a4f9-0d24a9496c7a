<template>
  <div class="work-stats">
    <van-nav-bar title="工作统计" left-arrow @click-left="onClickLeft" />
    <div class="header">
      <div class="date-picker-wrapper">
        <van-field
          v-model="dateRange"
          readonly
          label="日期范围"
          placeholder="选择日期范围"
          @click="showCalendar = true"
        />
        <van-button type="info" style="width: 80px; margin-right: 8px" size="small" @click="search">搜索</van-button>
      </div>
    </div>

    <div class="stats-list" v-if="!isShow">
      <div class="stats-item">
        <van-cell class="stats-cell" :value="stats.czCount || 0" value-class="stats-value">
          <template #icon>
            <van-icon name="records" class="stats-icon" />
          </template>
          <template #title>
            <span class="stats-title">处置数</span>
          </template>
          <template #value>
            <span class="stats-value">{{ stats.czCount }}</span>
          </template>
        </van-cell>
      </div>
      <div class="stats-item">
        <van-cell class="stats-cell" :value="stats.asczCount || 0" value-class="stats-value">
          <template #icon>
            <van-icon name="records" class="stats-icon" />
          </template>
          <template #title>
            <span class="stats-title">按时处置数</span>
          </template>
          <template #value>
            <span class="stats-value">{{ stats.asczCount }}</span>
          </template>
        </van-cell>
      </div>
      <div class="stats-item">
        <van-cell class="stats-cell" :value="stats.cqczCount || 0" value-class="stats-value">
          <template #icon>
            <van-icon name="records" class="stats-icon" />
          </template>
          <template #title>
            <span class="stats-title">超期处置数</span>
          </template>
          <template #value>
            <span class="stats-value">{{ stats.cqczCount }}</span>
          </template>
        </van-cell>
      </div>
      <div class="stats-item">
        <van-cell class="stats-cell" :value="stats.backCount || 0" value-class="stats-value">
          <template #icon>
            <van-icon name="records" class="stats-icon" />
          </template>
          <template #title>
            <span class="stats-title">返工数</span>
          </template>
          <template #value>
            <span class="stats-value">{{ stats.backCount }}</span>
          </template>
        </van-cell>
      </div>
    </div>

    <div class="stats-list" v-if="isShow">
      <div class="stats-card">
        <div class="card-header">上报统计数</div>
        <div class="card-content">
          <div class="stat-item">
            <div class="stat-value">{{ stats.reportCount || 0 }}</div>
            <div class="stat-label">上报数</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ stats.effectCount || 0 }}</div>
            <div class="stat-label">有效上报数</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ stats.noEffectCount || 0 }}</div>
            <div class="stat-label">无效上报数</div>
          </div>
        </div>
      </div>

      <div class="stats-card">
        <div class="card-header">核实统计数</div>
        <div class="card-content">
          <div class="stat-item">
            <div class="stat-value">{{ stats.shouldHesCount || 0 }}</div>
            <div class="stat-label">应核实数</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ stats.HesCount || 0 }}</div>
            <div class="stat-label">核实数</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ stats.OnHesCount || 0 }}</div>
            <div class="stat-label">按时核实数</div>
          </div>
        </div>
      </div>

      <div class="stats-card">
        <div class="card-header">核查统计数</div>
        <div class="card-content">
          <div class="stat-item">
            <div class="stat-value">{{ stats.shouldHecCount || 0 }}</div>
            <div class="stat-label">应核查数</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ stats.HecCount || 0 }}</div>
            <div class="stat-label">核查数</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ stats.OnHecCount || 0 }}</div>
            <div class="stat-label">按时核查数</div>
          </div>
        </div>
      </div>
    </div>

    <van-calendar
      v-model="showCalendar"
      type="range"
      :min-date="minDate"
      @confirm="onDateConfirm"
      @close="showCalendar = false"
    />
  </div>
</template>

<script>
import { formatDate } from '@/utils/date'
import { getYdWorkStatistics, getZybmWorkStatistics } from '@/api/workstats'
import { Calendar, Icon } from 'vant'

export default {
  name: 'WorkStats',
  components: {
    [Calendar.name]: Calendar,
    [Icon.name]: Icon,
  },
  data() {
    return {
      dateRange: '',
      showCalendar: false,
      minDate: new Date(2020, 0, 1),
      stats: {
        // 监督员统计
        czCount: 0,
        asczCount: 0,
        cqczCount: 0,
        backCount: 0,
        // 专业部门统计
        reportCount: 0,
        effectCount: 0,
        noEffectCount: 0,
        shouldHesCount: 0,
        HesCount: 0,
        OnHesCount: 0,
        shouldHecCount: 0,
        HecCount: 0,
        OnHecCount: 0,
      },
    }
  },
  created() {
    // 默认显示最近7天的数据
    const end = new Date()
    const start = new Date()
    start.setDate(start.getDate() - 7)

    this.startDate = formatDate(start)
    this.endDate = formatDate(end)
    this.search()
  },
  computed: {
    isShow() {
      return this.$store.state.user.roleType === '1'
    },
  },
  methods: {
    onClickLeft() {
      this.$router.back()
    },
    onDateConfirm([start, end]) {
      this.dateRange = `${formatDate(start)} 至 ${formatDate(end)}`
      this.startDate = formatDate(start)
      this.endDate = formatDate(end)
      this.showCalendar = false
      this.search()
    },
    async search() {
      try {
        let params = {
          beginTime: this.startDate,
          endTime: this.endDate,
        }
        if (this.startDate && this.endDate) {
          params.beginTime = this.startDate
          params.endTime = this.endDate
        } else {
          params = null
        }

        const response = this.isShow ? await getYdWorkStatistics({ params }) : await getZybmWorkStatistics({ params })

        this.stats = response.data
      } catch (error) {
        console.log(error)
        this.$toast.fail('获取数据失败')
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.work-stats {
  height: 100%;
  background: #f7f8fa;

  .header {
    background: #fff;

    .date-picker-wrapper {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }

  .stats-list {
    margin-top: 12px;
    padding: 0 16px;
    height: calc(100vh - 110px);

    .stats-item {
      width: 100%;
      height: 56px;
      background: #ffffff;
      border-radius: 16px;
      overflow: hidden;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.001);
      margin-bottom: 12px;

      .stats-cell {
        height: 100%;
        align-items: center;
        padding: 16px;

        .stats-icon {
          font-size: 24px;
          color: #4fc08d;
          margin-right: 8px;
        }

        .stats-title {
          font-size: 16px;
          color: #333;
        }

        .stats-value {
          font-size: 20px;
          color: #1989fa;
          font-weight: 500;
        }

        &::after {
          display: none;
        }
      }

      &:last-child {
        margin-bottom: 0;
      }
    }

    .stats-card {
      background: #fff;
      border-radius: 8px;
      margin-bottom: 12px;
      padding: 16px;

      .card-header {
        font-size: 16px;
        font-weight: 500;
        color: #333;
        margin-bottom: 16px;
        text-align: center;
      }

      .card-content {
        display: flex;
        justify-content: space-around;

        .stat-item {
          text-align: center;

          .stat-value {
            font-size: 20px;
            color: #1989fa;
            font-weight: 500;
            margin-bottom: 4px;
          }

          .stat-label {
            font-size: 14px;
            color: #666;
          }
        }
      }

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
</style>
<style>
.stats-value {
  font-size: 16px;
  color: #428ffc;
  font-weight: 500;
}
</style>
