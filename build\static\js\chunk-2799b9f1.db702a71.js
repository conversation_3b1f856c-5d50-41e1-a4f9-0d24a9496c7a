(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2799b9f1"],{1766:function(t,e,i){},"207e":function(t,e,i){"use strict";var s=function(){var t=this,e=t._self._c;return e("div",{staticClass:"vant-upload-file"},[e("van-uploader",{ref:"upload",attrs:{"max-count":t.limit,accept:t.accept,disabled:t.$attrs.disabled,"max-size":1024*t.fileSize*1024,"before-read":t.handleBeforeUpload,"after-read":t.handleAfterRead,"upload-text":t.uploadText,"upload-icon":t.uploadIcon,capture:"camera"},scopedSlots:t._u([{key:"default",fn:function(){return[t._t("default",(function(){return[e("div",{staticClass:"upload-icon-wrapper"},[e("van-icon",{staticClass:"upload-icon",attrs:{name:"photograph"}})],1)]}))]},proxy:!0}],null,!0),model:{value:t.internalFileList,callback:function(e){t.internalFileList=e},expression:"internalFileList"}}),t.showFileList&&t.fileList.length>0?e("div",{staticClass:"file-list-container"},[e("div",{staticClass:"file-list-title"},[t._v("已上传文件")]),t.$attrs.disabled?[e("div",{staticClass:"file-list-wrapper"},[t._l(t.fileList,(function(i,s){return e("div",{key:i.uid||s,staticClass:"file-item disabled"},[e("div",{staticClass:"file-info",on:{click:function(e){return t.handlePreview(i)}}},[e("van-icon",{staticClass:"file-icon",attrs:{name:"description"}}),e("div",{staticClass:"file-name van-ellipsis"},[t._v(t._s(i.name))])],1)])})),t.fileList.length>2?e("van-button",{staticClass:"toggle-button",attrs:{size:"mini",type:"default"},on:{click:t.toggleExpand}},[t._v(" "+t._s(t.isExpanded?"收起":"展开")+" ")]):t._e()],2)]:t._l(t.fileList,(function(i,s){return e("div",{key:i.uid||s,staticClass:"file-item"},[e("div",{staticClass:"file-info",on:{click:function(e){return t.handlePreview(i)}}},[e("van-icon",{staticClass:"file-icon",attrs:{name:"description"}}),e("div",{staticClass:"file-name van-ellipsis"},[t._v(t._s(i.name))])],1),e("div",{staticClass:"file-actions"},[e("van-icon",{staticClass:"delete-icon",attrs:{name:"delete"},on:{click:function(e){return t.handleFileDelete(s)}}})],1)])}))],2):t._e(),t.showTip?e("div",{staticClass:"upload-tip"},[e("p",[e("span",[t._v("支持格式："+t._s(t.fileTypeText))]),t.fileSize?e("span",[t._v("，大小不超过 "+t._s(t.fileSize)+"MB")]):t._e()])]):t._e()],1)},a=[],l=(i("e9f5"),i("910d"),i("7d54"),i("ab43"),i("a732"),i("0a5a")),n=i("4260"),o={name:"index",props:{limit:{type:Number,default:4},accept:{type:String,default:"image/*"},action:{type:String,default:"/prod-api/sysUploadFile/uploadFile"},autoUpload:{type:Boolean,default:!0},fileList:{type:Array,default:()=>[]},data:{type:Object,default:()=>({})},name:{type:String,default:"multipartFile"},value:[String,Object,Array,Number],fileSize:{type:Number,default:20},fileType:{type:Array,default:()=>["doc","docx","pdf","txt","xls","xlsx","png","jpg","jpeg","gif","mp3","mp4","mov","avi"]},isShowTip:{type:Boolean,default:!0},showFileList:{type:Boolean,default:!0},uploadIcon:{type:String,default:"photograph"},uploadText:{type:String,default:"上传文件"}},data(){return{headers:{Authorization:"Bearer "+Object(l["a"])()},internalFileList:[],isExpanded:!1,uploading:!1}},computed:{showTip(){return!this.$attrs.disabled&&(this.isShowTip&&(this.fileType||this.fileSize))},fileTypeText(){return this.fileType.join("、")}},watch:{fileList:{handler(t){this.internalFileList=t.map(t=>({url:Object(n["c"])(t),name:this.getFileName(t),isImage:/\.(jpeg|jpg|gif|png|webp)$/i.test(t||"")}))},immediate:!0},value:{handler(t){t?(this.fetchFiles(t),this.$emit("change",t)):(this.internalFileList=[],this.$emit("change",""))},immediate:!0},internalFileList:{handler(t){if(t&&t.length>0){const e=t.map(t=>t.uid||"").filter(Boolean).join(",");e&&e!==this.value&&(this.$emit("input",e),this.$emit("change",e))}else this.value&&(this.$emit("input",""),this.$emit("change",""))},deep:!0}},created(){this.value&&!this.internalFileList.length&&this.fetchFiles(this.value)},methods:{fetchFiles(t){if(t){const e=t.split(",").filter(Boolean).map(t=>({name:this.getFileName(t),url:Object(n["c"])(t),uid:t,isImage:/\.(jpeg|jpg|gif|png|webp)$/i.test(t||"")}));this.$emit("update:fileList",e),this.internalFileList=e}},handleBeforeUpload(t){if(this.fileType&&this.fileType.length>0){let e="";t.name.lastIndexOf(".")>-1&&(e=t.name.slice(t.name.lastIndexOf(".")+1).toLowerCase());const i=this.fileType.some(i=>t.type.indexOf(i)>-1||!(!e||i.toLowerCase()!==e));if(!i)return this.$toast.fail(`文件格式不正确, 请上传${this.fileType.join("/")}格式文件!`),!1}if(this.fileSize){const e=t.size/1024/1024<this.fileSize;if(!e)return this.$toast.fail(`上传文件大小不能超过 ${this.fileSize}MB!`),!1}return!0},handleAfterRead(t){this.autoUpload?this.uploadFile(t):this.$emit("select",t)},uploadFile(t){this.uploading=!0;const e=new FormData;t.file&&e.append(this.name,t.file),this.data&&Object.keys(this.data).forEach(t=>{e.append(t,this.data[t])}),t.status="uploading",t.message="上传中...",fetch(this.action,{method:"POST",headers:this.headers,body:e}).then(t=>t.json()).then(e=>{if(this.uploading=!1,200===e.code){if(t.status="done",t.message="上传成功",e.data){Object.assign(t,{url:Object(n["c"])(e.data),uid:e.data});const i=this.internalFileList.map(t=>t.uid||"").filter(Boolean).join(",");this.$emit("input",i),this.$emit("change",i)}this.$emit("success",{response:e,file:t,fileList:this.internalFileList})}else t.status="failed",t.message=e.msg||"上传失败",this.$toast.fail("文件上传失败，请重新上传"),this.$emit("error","文件上传失败，请重新上传")}).catch(e=>{this.uploading=!1,t.status="failed",t.message="上传失败",this.$toast.fail("文件上传失败，请重新上传"),this.$emit("error",e.message||"文件上传失败，请重新上传")})},handleFileDelete(t){this.$dialog.confirm({title:"提示",message:"是否确认删除？删除后将无法恢复"}).then(()=>{const e=this.internalFileList[t];this.internalFileList.splice(t,1),this.$emit("remove",e),this.$emit("update:fileList",[...this.internalFileList]),this.$emit("input",this.internalFileList.map(t=>t.uid).join(","))}).catch(()=>{})},handlePreview(t){if(this.$emit("preview",t),t.isImage){const e=this.internalFileList.filter(t=>t.isImage).map(t=>t.url),i=e.indexOf(t.url);this.$imagePreview({images:e,startPosition:i>-1?i:0,closeable:!0})}else t.url&&window.open(t.url)},toggleExpand(){this.isExpanded=!this.isExpanded},getFileName(t){return t?t.lastIndexOf("/")>-1?t.slice(t.lastIndexOf("/")+1).toLowerCase():t:""},submit(){if(this.internalFileList.some(t=>"uploading"===t.status))return void this.$toast("有文件正在上传中，请稍后再试");const t=this.internalFileList.filter(t=>!t.status||"failed"===t.status);0!==t.length?t.forEach(t=>{this.uploadFile(t)}):this.$toast("没有需要上传的文件")},clearFiles(){this.internalFileList=[],this.$emit("update:fileList",[]),this.$emit("input","")}}},r=o,d=(i("58d9"),i("2877")),c=Object(d["a"])(r,s,a,!1,null,"6abab3f2",null);e["a"]=c.exports},"31ac":function(t,e,i){},3224:function(t,e,i){"use strict";i("f833")},"58d9":function(t,e,i){"use strict";i("629c")},5945:function(t,e,i){"use strict";var s=function(){var t=this,e=t._self._c;return e("div",{staticClass:"map-container"},[e("div",{ref:"mapContainer",attrs:{id:"map"}}),t.currentAddress?e("div",{staticClass:"address-info"},[e("p",[t._v("经纬度: "+t._s(t.currentPosition.lat)+", "+t._s(t.currentPosition.lng))]),e("p",[t._v("地址: "+t._s(t.currentAddress))])]):t._e(),e("div",{staticStyle:{position:"absolute",bottom:"5px",left:"178px","z-index":"9999",display:"flex",gap:"10px"}},[t.type?e("van-button",{attrs:{disabled:t.isCanPoint,size:"small",type:"primary"},on:{click:t.flag}},[t._v("标记位置")]):t._e()],1)])},a=[],l=(i("e9f5"),i("ab43"),{name:"MapComponent",props:{coordinates:{type:[Array,Object],default:null}},data(){return{type:1,editMode:!1,map:null,marker:null,locationCircle:null,currentPosition:{lat:null,lng:null},isCanPoint:!1,currentAddress:"",tiandituKey:"301de34e264e4a45b3d000d5cff0a870"}},mounted(){this.initMap()},methods:{initMap(){if(this.map=L.map("map",{center:[29.110764,119.630857],zoom:14,zoomControl:!0,attributionControl:!1}),L.tileLayer("https://t0.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk="+this.tiandituKey,{maxZoom:18,tileSize:256,zoomOffset:0}).addTo(this.map),L.tileLayer("https://t0.tianditu.gov.cn/cia_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk="+this.tiandituKey,{maxZoom:18,tileSize:256,zoomOffset:0}).addTo(this.map),this.map.on("click",this.handleMapClick),this.coordinates){console.log("this.coordinates",this.coordinates),this.type=0;const t=Array.isArray(this.coordinates)?this.coordinates[0]:this.coordinates.lng,e=Array.isArray(this.coordinates)?this.coordinates[1]:this.coordinates.lat;this.currentPosition={lat:e,lng:t},this.reverseGeocode(e,t),this.addInitialMarker(e,t)}},handleMapClick(t){if(this.isCanPoint||this.editMode){const{lat:e,lng:i}=t.latlng;this.currentPosition={lat:e,lng:i},this.marker&&(this.map.removeLayer(this.marker),this.marker=null),this.marker=L.marker([e,i]).addTo(this.map),this.reverseGeocode(e,i)}this.isCanPoint=!1,this.editMode=!1},async reverseGeocode(t,e){try{const i=await fetch(`https://api.tianditu.gov.cn/geocoder?postStr={'lon':${e},'lat':${t},'ver':1}&type=geocode&tk=${this.tiandituKey}`),s=await i.json();"0"===s.status?(console.log(s),this.currentAddress=s.result.formatted_address,this.$emit("locationSelected",s.result)):this.currentAddress="获取地址失败"}catch(i){console.error("逆地理编码失败:",i),this.currentAddress="获取地址失败"}},addInitialMarker(t,e){let i=this;i.marker&&i.map.removeLayer(i.marker),i.marker=L.marker([t,e]).addTo(i.map),i.map.setView([t,e],12)},flag(){this.isCanPoint=!0},bianji(){this.editMode=!0}}}),n=l,o=(i("7b83"),i("2877")),r=Object(o["a"])(n,s,a,!1,null,"673de8dc",null);e["a"]=r.exports},"629c":function(t,e,i){},"6e29":function(t,e,i){"use strict";var s=function(){var t=this,e=t._self._c;return e("div",{staticClass:"idioms-selector"},[e("van-button",{staticClass:"action-btn",class:{"is-plain":t.plain},attrs:{type:t.type,plain:t.plain,disabled:t.disabled},on:{click:t.showPopup}},[t._t("default",(function(){return[t._v(t._s(t.buttonText))]}))],2),e("van-popup",{staticClass:"idioms-popup",style:{maxHeight:"70%"},attrs:{round:"",position:"bottom"},model:{value:t.visible,callback:function(e){t.visible=e},expression:"visible"}},[e("div",{staticClass:"popup-header"},[e("div",{staticClass:"popup-title"},[t._v("选择惯用语")]),e("van-icon",{staticClass:"close-icon",attrs:{name:"cross"},on:{click:t.closePopup}})],1),e("div",{staticClass:"popup-content"},[t.loading?e("div",{staticClass:"loading-container"},[e("van-loading",{attrs:{type:"spinner",color:"#1989fa"}}),e("span",{staticClass:"loading-text"},[t._v("加载中...")])],1):0===t.idiomsList.length?e("div",{staticClass:"empty-container"},[e("van-empty",{attrs:{description:"暂无惯用语"}}),e("div",{staticClass:"empty-action"},[e("van-button",{attrs:{plain:"",type:"info",size:"small"},on:{click:t.goToSettings}},[t._v(" 去添加 ")])],1)],1):e("div",{staticClass:"idioms-list"},t._l(t.idiomsList,(function(i,s){return e("div",{key:s,staticClass:"idiom-item",on:{click:function(e){return t.selectIdiom(i.phrase)}}},[e("div",{staticClass:"idiom-content"},[t._v(t._s(i.phrase))])])})),0)]),e("div",{staticClass:"popup-footer"},[e("van-button",{attrs:{block:"",type:"info"},on:{click:t.closePopup}},[t._v("取消")])],1)])],1)},a=[],l=(i("14d9"),i("2934")),n={name:"IdiomsSelector",props:{buttonText:{type:String,default:"选择惯用语"},type:{type:String,default:"info"},plain:{type:Boolean,default:!0},target:{type:String,default:""},disabled:{type:Boolean,default:!1}},data(){return{visible:!1,idiomsList:[],loading:!1,queryParams:{pageNum:1,pageSize:50}}},methods:{showPopup(){this.disabled||(this.visible=!0,this.getIdiomsList())},closePopup(){this.visible=!1},async getIdiomsList(){try{this.loading=!0;const t=await Object(l["j"])(this.queryParams);200===t.code?this.idiomsList=t.rows||[]:this.$toast.fail(t.msg||"获取惯用语失败")}catch(t){console.error("获取惯用语列表失败:",t),this.$toast.fail("获取惯用语失败")}finally{this.loading=!1}},selectIdiom(t){this.$emit("select",t),this.closePopup()},goToSettings(){this.$router.push("/IdiomaticExpressions"),this.closePopup()}}},o=n,r=(i("7d90"),i("2877")),d=Object(r["a"])(o,s,a,!1,null,"24e0cf40",null);e["a"]=d.exports},"7b83":function(t,e,i){"use strict";i("31ac")},"7d90":function(t,e,i){"use strict";i("1766")},a5d1:function(t,e,i){t.exports=i.p+"static/img/map-placeholder.feeea3e5.png"},eeb8:function(t,e,i){"use strict";var s=function(){var t=this,e=t._self._c;return e("div",{staticClass:"tab-switch"},[e("van-tabs",{attrs:{border:!1,"line-width":20,"line-height":"2px",color:"#1989fa","title-active-color":"#1989fa","title-inactive-color":"#666"},on:{change:t.handleChange},model:{value:t.modelValue,callback:function(e){t.modelValue=e},expression:"modelValue"}},t._l(t.tabs,(function(t,i){return e("van-tab",{key:i,attrs:{title:t.label,name:t.value}})})),1)],1)},a=[],l={name:"TabSwitch",props:{tabs:{type:Array,default:()=>[]},value:{type:[String,Number],default:""}},computed:{modelValue:{get(){return this.value},set(t){this.$emit("input",t)}}},methods:{handleChange(t){this.$emit("change",t)}}},n=l,o=(i("3224"),i("2877")),r=Object(o["a"])(n,s,a,!1,null,"185e5f8f",null);e["a"]=r.exports},f833:function(t,e,i){}}]);