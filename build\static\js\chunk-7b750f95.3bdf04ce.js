(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7b750f95"],{1766:function(t,e,i){},"21a5":function(t,e,i){},"54c3":function(t,e,i){},"6e29":function(t,e,i){"use strict";var s=function(){var t=this,e=t._self._c;return e("div",{staticClass:"idioms-selector"},[e("van-button",{staticClass:"action-btn",class:{"is-plain":t.plain},attrs:{type:t.type,plain:t.plain,disabled:t.disabled},on:{click:t.showPopup}},[t._t("default",(function(){return[t._v(t._s(t.buttonText))]}))],2),e("van-popup",{staticClass:"idioms-popup",style:{maxHeight:"70%"},attrs:{round:"",position:"bottom"},model:{value:t.visible,callback:function(e){t.visible=e},expression:"visible"}},[e("div",{staticClass:"popup-header"},[e("div",{staticClass:"popup-title"},[t._v("选择惯用语")]),e("van-icon",{staticClass:"close-icon",attrs:{name:"cross"},on:{click:t.closePopup}})],1),e("div",{staticClass:"popup-content"},[t.loading?e("div",{staticClass:"loading-container"},[e("van-loading",{attrs:{type:"spinner",color:"#1989fa"}}),e("span",{staticClass:"loading-text"},[t._v("加载中...")])],1):0===t.idiomsList.length?e("div",{staticClass:"empty-container"},[e("van-empty",{attrs:{description:"暂无惯用语"}}),e("div",{staticClass:"empty-action"},[e("van-button",{attrs:{plain:"",type:"info",size:"small"},on:{click:t.goToSettings}},[t._v(" 去添加 ")])],1)],1):e("div",{staticClass:"idioms-list"},t._l(t.idiomsList,(function(i,s){return e("div",{key:s,staticClass:"idiom-item",on:{click:function(e){return t.selectIdiom(i.phrase)}}},[e("div",{staticClass:"idiom-content"},[t._v(t._s(i.phrase))])])})),0)]),e("div",{staticClass:"popup-footer"},[e("van-button",{attrs:{block:"",type:"info"},on:{click:t.closePopup}},[t._v("取消")])],1)])],1)},a=[],n=(i("14d9"),i("2934")),o={name:"IdiomsSelector",props:{buttonText:{type:String,default:"选择惯用语"},type:{type:String,default:"info"},plain:{type:Boolean,default:!0},target:{type:String,default:""},disabled:{type:Boolean,default:!1}},data(){return{visible:!1,idiomsList:[],loading:!1,queryParams:{pageNum:1,pageSize:50}}},methods:{showPopup(){this.disabled||(this.visible=!0,this.getIdiomsList())},closePopup(){this.visible=!1},async getIdiomsList(){try{this.loading=!0;const t=await Object(n["j"])(this.queryParams);200===t.code?this.idiomsList=t.rows||[]:this.$toast.fail(t.msg||"获取惯用语失败")}catch(t){console.error("获取惯用语列表失败:",t),this.$toast.fail("获取惯用语失败")}finally{this.loading=!1}},selectIdiom(t){this.$emit("select",t),this.closePopup()},goToSettings(){this.$router.push("/IdiomaticExpressions"),this.closePopup()}}},l=o,r=(i("7d90"),i("2877")),c=Object(r["a"])(l,s,a,!1,null,"24e0cf40",null);e["a"]=c.exports},"7d90":function(t,e,i){"use strict";i("1766")},8080:function(t,e,i){"use strict";var s=function(){var t=this,e=t._self._c;return e("div",{staticClass:"custom-picker"},[e("div",{staticClass:"picker-trigger",on:{click:t.showPicker}},[t._t("trigger",(function(){return[e("div",{staticClass:"selected-value",class:{valueColor:t.showValue}},[t._v(" "+t._s(t.showValue||t.placeholder)+" ")]),e("van-icon",{attrs:{name:"arrow"}})]}))],2),e("van-popup",{attrs:{round:"",position:"bottom"},model:{value:t.isVisible,callback:function(e){t.isVisible=e},expression:"isVisible"}},[e("van-picker",{attrs:{columns:t.columns,"default-index":t.defaultIndex,"show-toolbar":"",title:t.title,loading:t.loading},on:{confirm:t.onConfirm,cancel:t.onCancel,change:t.onChange}})],1)],1)},a=[],n=(i("e9f5"),i("f665"),{name:"CustomPicker",props:{columns:{type:Array,default:()=>[]},value:{type:[String,Number],default:""},title:{type:String,default:"请选择"},placeholder:{type:String,default:"请选择"},loading:{type:Boolean,default:!1}},data(){return{isVisible:!1,defaultIndex:0}},computed:{showValue(){const t=this.columns.find(t=>t===this.value||"object"===typeof t&&t.value===this.value);return t?"object"===typeof t?t.text:t:""}},watch:{value:{handler(t){this.setDefaultIndex(t)},immediate:!0}},methods:{setDefaultIndex(t){try{this.defaultIndex=this.columns.findIndex(e=>e===t||"object"===typeof e&&e.value===t),-1===this.defaultIndex&&(this.defaultIndex=0)}catch(e){console.error("设置默认值失败:",e),this.defaultIndex=0}},showPicker(){this.isVisible=!0},onConfirm(t,e){this.isVisible=!1;const i="object"===typeof t?t.value:t;this.$emit("input",i),this.$emit("confirm",t,e)},onCancel(){this.isVisible=!1,this.$emit("cancel")},onChange(t,e,i){this.$emit("change",{picker:t,value:e,index:i})}}}),o=n,l=(i("c3d6"),i("2877")),r=Object(l["a"])(o,s,a,!1,null,"713dcdce",null);e["a"]=r.exports},"920f":function(t,e,i){"use strict";i.r(e);var s=function(){var t=this,e=t._self._c;return e("div",{staticClass:"container"},[e("van-nav-bar",{attrs:{title:"申请延期","left-arrow":"",fixed:""},on:{"click-left":function(e){return t.$router.go(-1)}}}),e("div",{staticClass:"remark-container"},[e("div",{staticClass:"remark-area"},[t._m(0),e("van-field",{attrs:{readonly:"",placeholder:"",autosize:""},model:{value:t.NumberOfExtensions,callback:function(e){t.NumberOfExtensions=e},expression:"NumberOfExtensions"}})],1),e("div",{staticClass:"remark-area"},[t._m(1),e("van-field",{attrs:{type:"number",placeholder:"请输入延期时间(数字)",rows:"1",autosize:""},on:{input:t.validateNumber},model:{value:t.dealNumber,callback:function(e){t.dealNumber=e},expression:"dealNumber"}})],1),e("div",{staticClass:"remark-area"},[t._m(2),e("van-field",{attrs:{readonly:"",placeholder:"",autosize:""},scopedSlots:t._u([{key:"input",fn:function(){return[e("custom-picker",{attrs:{columns:t.dealUnitOptions,placeholder:"请选择时间类型"},on:{confirm:t.onDealUnitConfirm},model:{value:t.dealUnit,callback:function(e){t.dealUnit=e},expression:"dealUnit"}})]},proxy:!0}])})],1),e("div",{staticClass:"remark-area"},[e("div",{staticClass:"remark-label-container"},[e("div",{staticClass:"remark-label"},[t._v("延期理由")]),e("idioms-selector",{on:{select:t.selectIdiom}},[t._v("选择惯用语")])],1),e("van-field",{attrs:{type:"textarea",placeholder:"请输入延期理由",rows:"1",autosize:""},model:{value:t.opinion,callback:function(e){t.opinion=e},expression:"opinion"}})],1)]),e("div",{staticClass:"bottom-btns"},[e("van-button",{staticClass:"cancel-btn",attrs:{plain:""},on:{click:t.onCancel}},[t._v("取消")]),e("van-button",{staticClass:"submit-btn",attrs:{type:"info"},on:{click:t.onSubmit}},[t._v("提交")])],1)],1)},a=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"remark-label-container"},[e("div",{staticClass:"remark-label"},[t._v("剩余延期次数")])])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"remark-label-container"},[e("div",{staticClass:"remark-label"},[t._v("延期时间")])])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"remark-label-container"},[e("div",{staticClass:"remark-label"},[t._v("时间类型")])])}],n=i("6e29"),o=i("2934"),l=i("8080"),r={name:"index",components:{IdiomsSelector:n["a"],CustomPicker:l["a"]},props:["id"],data(){return{NumberOfExtensions:"98",dealNumber:"",dealUnit:"",opinion:"",dealUnitOptions:[{text:"时",value:1},{text:"天",value:2},{text:"周",value:3},{text:"月",value:4}]}},computed:{},mounted(){this.initDefaultValues()},methods:{initDefaultValues(){this.dealUnit=2},selectIdiom(t){this.opinion=t},onCancel(){this.$dialog.confirm({title:"提示",message:"确定要取消申请吗？"}).then(()=>{this.$router.go(-1)}).catch(()=>{})},validateNumber(){const t=this.dealNumber.toString().trim();if(t){const e=t.replace(/[^\d]/g,"");(e!==t||e.length>1&&e.startsWith("0"))&&(this.dealNumber=e.replace(/^0+/,"")||"0")}},onDealUnitConfirm(t){console.log("选择的时间类型：",t)},onSubmit(){!this.dealNumber||parseInt(this.dealNumber)<=0?this.$toast("请输入有效的延期时间"):this.dealUnit?this.opinion.trim()?(this.$toast.loading({message:"提交中...",forbidClick:!0,duration:0}),Object(o["b"])({opinion:this.opinion,operateType:12,eventId:this.$route.query.id,dealNumber:parseInt(this.dealNumber),dealUnit:parseInt(this.dealUnit)}).then(t=>{200==t.code&&(this.$toast.clear(),this.$toast.success("提交成功"),setTimeout(()=>{this.$router.go(-1)},1e3))}).catch(t=>{this.$toast.clear(),this.$toast.fail("提交失败"),console.error("提交失败:",t)})):this.$toast("请输入延期理由"):this.$toast("请选择时间类型")}},watch:{}},c=r,u=(i("f95a"),i("2877")),d=Object(u["a"])(c,s,a,!1,null,"585fcd30",null);e["default"]=d.exports},c3d6:function(t,e,i){"use strict";i("21a5")},f95a:function(t,e,i){"use strict";i("54c3")}}]);