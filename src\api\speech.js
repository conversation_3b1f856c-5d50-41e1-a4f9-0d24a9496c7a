import request from '@/util/request'

// 语音识别接口 - 上传音频文件获取文字内容
export function speechToText(data) {
  return request({
    url: '/system/file/SpeechRecognition',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    timeout: 30000, // 设置30秒超时，因为语音识别可能需要较长时间
    // 添加请求拦截器来记录发送的数据
    transformRequest: [function (data, headers) {
      console.log('发送语音识别请求，数据类型:', typeof data);
      if (data instanceof FormData) {
        console.log('FormData字段:');
        for (let [key, value] of data.entries()) {
          if (value instanceof File) {
            console.log(`${key}: File(${value.name}, ${value.type}, ${value.size}字节)`);
          } else {
            console.log(`${key}:`, value);
          }
        }
      }
      return data;
    }]
  })
}
