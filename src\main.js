import Vue from 'vue'
import App from './App.vue'
import store from '@/store'
import router from './router'
import { getDicts } from '@/api/common'
import { getDictText, getImageUrl } from '@/util'
// 导入vantUI组件
import './plugins/vant.js'

// 导入全局样式
import '@/assets/styles/index.scss'

// bus
import bus from '@/util/bus'
// 注册全局上传图片组件
import MUpload from '@/components/m-upload'
Vue.component('MUpload', MUpload)

// 全局方法挂载
Vue.prototype.getDicts = getDicts
Vue.prototype.$bus = bus
Vue.prototype.$getImageUrl = getImageUrl
Vue.prototype.getDictText = getDictText

Vue.config.productionTip = false

import storeMixin from '@/store/mixin'
Vue.mixin(storeMixin)

import mixinAse from '@/util/mixinAse'
Vue.mixin(mixinAse)

new Vue({
  store,
  router,
  render: (h) => h(App),
}).$mount('#app')
