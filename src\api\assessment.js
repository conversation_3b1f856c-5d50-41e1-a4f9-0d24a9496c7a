import request from '@/util/request'

/**
 * 考核查询 审查员
 * @param {*} dictType
 * @returns
 */
export function getAssessList(params) {
  return request({
    url: '/road/assess/main/list',
    method: 'get',
    params,
  })
}

/**
 * 考核查询 查询单条详情
 */
export function getAssessDetail(params) {
  return request({
    url: '/road/assess/main/select',
    method: 'get',
    params,
  })
}

/**
 * 检查员获取扣分细则
 */
export function getSubtract(params) {
  return request({
    url: '/road/assess/check/getsubtract',
    method: 'get',
    params,
  })
}

/**
 * 开始检查 只修改状态
 */
export function startCheck(data) {
  return request({
    url: '/road/assess/startcheck/update',
    method: 'put',
    data,
  })
}

/**
 * 添加检查
 */
export function addCheck(data) {
  return request({
    url: '/road/assess/check/add',
    method: 'post',
    data,
  })
}
