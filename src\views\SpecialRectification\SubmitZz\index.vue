<!--整治提交-->
<template>
  <div class="problem-report">
    <!-- 导航栏 -->
    <van-nav-bar
        :title="isDetail ? '整治详情' : '专项整治'"
        left-arrow
        @click-left="onClickLeft"
    >
    </van-nav-bar>

    <!-- 表单内容 -->
    <div class="form-content">

      <!-- 基本信息 -->
      <van-cell-group class="form-group">
        <!-- 类型选择 -->
        <van-field
            size="large"
            label="类型"
            readonly
        >
          <template #input>
            <custom-picker
                v-model="formData.type1id"
                :columns="columns.type1Options"
                placeholder="请选择"
                @confirm="handleType1Change($event,true)"
                :disabled="isDetail"
            />
          </template>
        </van-field>

        <!-- 大类选择 -->
        <van-field
            size="large"
            label="大类"
            readonly
        >
          <template #input>
            <custom-picker
                v-model="formData.type2id"
                :columns="columns.type2Options"
                placeholder="请选择"
                @confirm="handleType2Change($event,true)"
                :disabled="isDetail"
            />
          </template>
        </van-field>

        <!-- 小类选择 -->
        <van-field
            size="large"
            label="小类"
            readonly
        >
          <template #input>
            <custom-picker
                v-model="formData.type3id"
                :columns="columns.type3Options"
                placeholder="请选择"
                @confirm="handleType3Change($event,true)"
                :disabled="isDetail"
            />
          </template>
        </van-field>

        <!-- 立案标准选择 -->
        <van-field
            size="large"
            label="立案标准"
            readonly
        >
          <template #input>
            <custom-picker
                v-model="formData.type4id"
                :columns="columns.type4Options"
                placeholder="请选择"
                @confirm="onPickerConfirm('type4id', $event)"
                :disabled="isDetail"
            />
          </template>
        </van-field>
      </van-cell-group>

      <van-cell-group class="form-group">
        <!-- 位置信息 -->
        <van-field
            size="large"
            label="位置选择"
            readonly
            placeholder=""
            right-icon="arrow"
        >
          <template #right-icon>
            <div class="section-content">
              <van-button plain type="primary" size="small" class="action-btn" @click="!isDetail?showMapDialog = true:false" :disabled="isDetail">{{ formData.areaid?'已定位':'选择定位' }}</van-button>
            </div>
          </template>
        </van-field>

        <!-- 位置信息 -->
        <van-field
            size="large"
            label="位置描述"
            readonly
            placeholder=""
            right-icon="arrow"
        >
          <template #right-icon>
            <div class="section-content">
              <van-button plain type="primary" size="small" class="action-btn" @click="!isDetail?generateAddress():false" :disabled="isDetail">{{ formData.address?'已生成':'自动生成' }}</van-button>
            </div>
          </template>
        </van-field>

        <van-field
            type="textarea"
            v-model="formData.address"
            label=""
            placeholder="请输入位置描述"
            rows="2"
            autosize
            maxlength="50"
            show-word-limit
            :readonly="isDetail"
        />

        <!-- 问题描述 -->
        <van-field
            size="large"
            label="问题描述"
            readonly
            placeholder=""
            right-icon="arrow"
        >
          <template #right-icon>
            <div class="section-content">
              <idioms-selector @select="selectIdiom" :disabled="isDetail">选择惯用语</idioms-selector>
            </div>
          </template>
        </van-field>
        <van-field
            type="textarea"
            v-model="formData.eventdesc"
            label=""
            placeholder="请输入问题描述"
            rows="2"
            autosize
            maxlength="50"
            show-word-limit
            :readonly="isDetail"
        />

        <!-- 图片上传 -->
        <div class="form-section">
          <div class="section-label">图片选择（最少一张）</div>
        </div>
        <div class="uploader-wrapper">
          <vantFileUpload
              v-model="formData.fileStr"
              :file-type="['jpg', 'png']"
              @change="handleFileChange"
              :disabled="isDetail"
          />
        </div>

        <!-- 是否自办结 -->
        <van-field
            size="large"
            label="是否自办结"
            readonly
            placeholder=""
            right-icon="arrow"
        >
          <template #right-icon>
            <van-radio-group v-model="formData.isSelfComplete" direction="horizontal" :disabled="isDetail">
              <van-radio :name="1">是</van-radio>
              <van-radio :name="0">否</van-radio>
            </van-radio-group>
          </template>
        </van-field>
      </van-cell-group>

    </div>

    <!-- 提交按钮 -->
    <div class="submit-btn-wrapper">
      <van-button type="info" block @click="isDetail ? onClickLeft() : onSubmit()">{{ isDetail ? '返回' : '提交' }}</van-button>
    </div>
    <!-- 地图弹窗 -->
    <van-popup
        v-model="showMapDialog"
        position="bottom"
        :style="{ height: '80%', width: '100%' }"
    >
      <div class="map-dialog">
        <Map
            v-if="showMapDialog"
            ref="mapComponent"
            @locationSelected="handleLocationSelected"
        />
        <div class="map-dialog-footer">
          <van-button type="info" block @click="confirmLocation">确认位置</van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import CustomPicker from '@/components/CustomPicker/index.vue'
import TabSwitch from '@/components/TabSwitch/index.vue'
import Map from '@/components/map/index.vue'
import vantFileUpload from "@/components/vant-file-upload";
import IdiomsSelector from "@/components/IdiomsSelector";
import {addAj, getProblemDetail} from "@/api/common";
import {getDownLoadUrl} from "@/util";
export default {
  name: 'ProblemReport',
  components: {
    CustomPicker,
    TabSwitch,
    Map,
    vantFileUpload,
    IdiomsSelector
  },
  data() {
    return {
      showMapDialog: false,  // 新增弹窗控制状态
      // 详情数据
      detailInfo: null,
      isDetail: false,
      // 表单数据
      formData: {
        source: "",
        createid: "",
        type1id: '',
        type2id: '',
        type3id: '',
        type4id: '',
        areaid:"",  //定位获取所属区县
        streetid:"", //定位获取所属街道
        address: '', //位置描述
        eventdesc: '', //问题描述

        fileStr: "",
        isSelfComplete: 0
      },

      // 选择器数据
      columns: {
        type1Options: [
          { text: '类型1', value: '1' },
          { text: '类型2', value: '2' },
          { text: '类型3', value: '3' }
        ],
        type2Options: [
          { text: '大类1', value: '1' },
          { text: '大类2', value: '2' },
          { text: '大类3', value: '3' }
        ],
        type3Options: [
          { text: '小类1', value: '1' },
          { text: '小类2', value: '2' },
          { text: '小类3', value: '3' }
        ],
        type4Options: [
          { text: '标准1', value: '1' },
          { text: '标准2', value: '2' },
          { text: '标准3', value: '3' }
        ]
      },
      areaOptions: []
    };
  },
  created() {

  },
  mounted() {
    this.getDictsList()
    this.initParams()
    // 检查是否需要加载详情
    this.checkDetailMode()
  },
  methods: {
    // 检查是否是详情模式
    async checkDetailMode() {
      const detailId = this.$route.query.detailId
      if (detailId) {
        this.isDetail = true
        this.$toast.loading({
          message: '加载详情...',
          forbidClick: true,
          duration: 0
        })
        try {
          const res = await getProblemDetail(detailId)
          if (res.code === 200) {
            this.detailInfo = res.data
            this.fillFormData(res.data)
          } else {
            this.$toast.fail(res.msg || '获取详情失败')
          }
        } catch (error) {
          console.error('获取详情失败:', error)
          this.$toast.fail('获取详情失败')
        } finally {
          this.$toast.clear()
        }
      }
    },

    // 填充表单数据
    fillFormData(data) {
      // 填充基本数据
      this.formData.type1id = data.type1id || ''
      this.formData.areaid = data.areaid || ''
      this.formData.streetid = data.streetid || ''
      this.formData.address = data.address || ''
      this.formData.eventdesc = data.eventdesc || ''
      this.formData.isSelfComplete = data.isSelfComplete || 0
      this.formData.fileStr = data.fileStr || ''

      // 触发级联选择器的变更
      if (data.type1id) {
        this.handleType1Change(data.type1id, false)
        // 延迟设置以确保选项已加载
        setTimeout(() => {
          this.formData.type2id = data.type2id || ''
          if (data.type2id) {
            this.handleType2Change(data.type2id, false)
            setTimeout(() => {
              this.formData.type3id = data.type3id || ''
              if (data.type3id) {
                this.handleType3Change(data.type3id, false)
                setTimeout(() => {
                  this.formData.type4id = data.type4id || ''
                }, 300)
              }
            }, 300)
          }
        }, 300)
      }
    },

    initParams() {
      this.formData.source = "2"; //移动端默认监督员上报
      this.formData.createid = this.user.nickName
    },

    //获取问题类型
    getDictsList() {
      this.getDicts('zhcg_wtlx').then((response) => {
        this.columns.type1Options = response.data.map(item => ({
          text: item.dictLabel,
          value: item.dictValue
        }))

        // 如果是详情模式，重新加载type2和type3的选项
        if (this.isDetail && this.detailInfo) {
          this.fillFormData(this.detailInfo)
        }
      })
      this.getDicts('county').then((response) => {
        this.areaOptions = response.data.map(item => ({
          label: item.dictLabel,
          value: item.dictValue
        }))
      })
    },

    //问题类型切换 isCurrentChoose:是否默认勾选第一项
    handleType1Change(res,isCurrentChoose) {
      if (res) {
        this.getDicts(res.value?res.value:res).then((response) => {
          this.columns.type2Options = response.data.map(item => ({
            text: item.dictLabel,
            value: item.dictValue
          }))
          if (isCurrentChoose) this.formData.type2id = this.columns.type2Options[0].value
          this.handleType2Change(this.formData.type2id, isCurrentChoose?true:false)
        })
      } else {
        this.columns.type2Options = []
        this.formData.type2id = ""
        this.handleType2Change(this.formData.type2id,false)
      }
    },
    //大类切换
    handleType2Change(res,isCurrentChoose) {
      if (res) {
        this.getDicts(res.value?res.value:res).then((response) => {
          this.columns.type3Options = response.data.map(item => ({
            text: item.dictLabel,
            value: item.dictValue
          }))
          if (isCurrentChoose) this.formData.type3id = this.columns.type3Options[0].value
          this.handleType3Change(this.formData.type3id, isCurrentChoose?true:false)
        })
      } else {
        this.columns.type3Options = []
        this.formData.type3id = ""
        this.handleType3Change(this.formData.type3id,false)
      }
    },
    //小类切换
    handleType3Change(res,isCurrentChoose) {
      if (res) {
        this.getDicts(res.value?res.value:res).then((response) => {
          this.columns.type4Options = response.data.map(item => ({
            text: item.dictLabel,
            value: item.dictValue
          }))
          if (isCurrentChoose) this.formData.type4id = this.columns.type4Options[0].value
        })
      } else {
        this.columns.type4Options = []
        this.formData.type4id = ""
      }
    },

    // 返回上一页
    onClickLeft() {
      this.$router.go(-1);
    },

    // 选择器确认
    onPickerConfirm(type, value) {
      console.log(`${type} selected:`, value);
    },

    handleLocationSelected(e) {
      this.formData.areaid = this.areaOptions.find(item => item.label == e.addressComponent.county).value?this.areaOptions.find(item => item.label == e.addressComponent.county).value:""
      this.formData.streetid = e.addressComponent.road
      this.formData.address = e.formatted_address
    },
    confirmLocation() {
      this.showMapDialog = false
    },
    // 提交表单
    onSubmit() {
      // 表单验证
      if (!this.formData.type1id) {
        return this.$toast('请选择类型');
      }
      if (!this.formData.type2id) {
        return this.$toast('请选择大类');
      }
      if (!this.formData.type3id) {
        return this.$toast('请选择小类');
      }
      if (!this.formData.type4id) {
        return this.$toast('请选择立案标准');
      }
      if (!this.formData.address) {
        return this.$toast('请输入位置描述');
      }
      if (!this.formData.eventdesc) {
        return this.$toast('请输入问题描述');
      }
      if (this.formData.fileStr.length === 0) {
        return this.$toast('请至少上传一张图片');
      }

      // 提交表单
      this.$toast.loading({
        message: '提交中...',
        forbidClick: true,
      });
      this.formData.isShow = Number(this.$route.query.taskType)
      this.formData.specialTaskId = Number(this.$route.query.id)

      // 如果是详情模式，添加id
      if (this.isDetail && this.detailInfo) {
        this.formData.id = this.detailInfo.id
      }

      addAj(this.formData).then(res => {
        if (res.code == 200) {
          this.$toast.success('提交成功');
          setTimeout(() => {
            this.$router.go(-2);
          }, 1000);
        } else {
          this.$toast.fail(res.msg || '提交失败');
        }
      }).catch(err => {
        console.error('提交失败:', err);
        this.$toast.fail('提交失败');
      }).finally(() => {
        this.$toast.clear();
      })
    },

    // 选择惯用语
    selectIdiom(content) {
      this.formData.eventdesc = content;
    },

    // 处理文件变化
    handleFileChange(val) {
      console.log('文件已更新:', val);
      // 如果需要在文件变化时做一些操作，可以在这里处理
      // 例如：自动保存草稿
    },

    // 自动生成地址
    generateAddress() {
      if (!this.formData.areaid) {
        this.$toast('请先选择位置');
        return;
      }

      // 这里可以根据已选位置自动生成地址描述
      const area = this.formData.areaid || '';
      const street = this.formData.streetid || '';
      this.formData.address = `${area}${street}附近`;
      this.$toast('地址已自动生成');
    }
  }
};
</script>

<style lang="scss" scoped>
.problem-report {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 60px;
}

// 表单内容样式
.form-content {
  padding-bottom: 20px;
}

.form-group {
  background-color: #fff;
  margin-bottom: 10px;
}

// 自定义 van-field 样式
::v-deep .van-field {
  font-size: 14px;
}

::v-deep .van-field__label {
  width: 90px;
  color: #333;
}

::v-deep .van-field__placeholder {
  color: #999;
}

::v-deep .van-field__right-icon {
  color: #ccc;
}

// 表单区块样式
.form-section {
  padding: 0 15px;
  margin-top: 15px;

  .section-label {
    font-size: 14px;
    color: #333;
    margin-bottom: 10px;
  }

  .section-content {
    display: flex;
    justify-content: flex-end;
  }
}

// 按钮样式
.action-btn {
  height: 32px;
  padding: 0 15px;
  font-size: 13px;
  border-radius: 4px;
  background-color: #e8f3ff;
  border-color: #e8f3ff;
  color: #1989fa;
}

// 自动生成文本样式
.auto-text {
  padding: 0 15px;
  font-size: 14px;
  color: #999;
  margin-top: 5px;
}

// 文本域样式
.textarea-wrapper {
  padding: 0 15px;
  margin-top: 10px;
}

.problem-textarea {
  background-color: #fff;
  border-radius: 4px;

  ::v-deep .van-field__control {
    min-height: 80px;
  }
}

// 上传组件样式
.uploader-wrapper {
  padding: 0 15px;

  ::v-deep .van-uploader__upload {
    background-color: #f7f8fa;
    border: 1px dashed #dcdee0;
  }
}

// 是否自办结样式
.self-complete {
  display: flex;
  align-items: center;
  margin-top: 20px;

  .section-label {
    margin-bottom: 0;
    margin-right: 20px;
  }

  .radio-wrapper {
    flex: 1;
  }

  ::v-deep .van-radio {
    margin-right: 20px;
  }

  ::v-deep .van-radio__icon--checked .van-icon {
    background-color: #1989fa;
    border-color: #1989fa;
  }

  ::v-deep .van-radio__icon {
    font-size: 16px;
  }
}

// 提交按钮样式
.submit-btn-wrapper {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 10px 15px;
  background-color: #fff;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);

  ::v-deep .van-button {
    height: 44px;
    line-height: 44px;
    font-size: 16px;
    border-radius: 4px;
  }
}

// 适配 iPhone X 等带底部安全区域的机型
@supports (padding-bottom: constant(safe-area-inset-bottom)) {
  .submit-btn-wrapper {
    padding-bottom: calc(10px + constant(safe-area-inset-bottom));
  }
}

@supports (padding-bottom: env(safe-area-inset-bottom)) {
  .submit-btn-wrapper {
    padding-bottom: calc(10px + env(safe-area-inset-bottom));
  }
}

// 自定义选择器样式
::v-deep .van-field__input {
  .custom-picker {
    width: 100%;
    text-align: right;
  }

  .selected-value {
    color: #323233;
    font-size: 14px;
  }
}

// 新增地图弹窗样式
.map-dialog {
  height: 100%;
  display: flex;
  flex-direction: column;

  &-footer {
    padding: 10px;
    background: #fff;
    box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
  }
}

// 调整地图容器高度
.MapContainer {
  height: 0;  // 隐藏底部固定地图
}

// 导航栏右侧按钮样式
.nav-right {
  display: flex;
  align-items: center;

  .save-draft {
    font-size: 14px;
    color: #1989fa;
  }
}
</style>
