import Vue from 'vue'
import VueRouter from 'vue-router'
import { getToken } from '@/util/auth'

Vue.use(VueRouter)

const routes = [
  {
    path: '/',
    component: () => import('@/views/layout'),
    children: [
      {
        // 首页
        name: 'Home',
        path: '', // 默认子路由
        component: () => import('@/views/home'),
        meta: { title: '首页' },
      },
      {
        // 登录页
        name: 'Login',
        path: '/login',
        component: () => import('@/views/login'),
        meta: { title: '登录' },
      },
      {
        // 个人中心
        name: 'PersonCenter',
        path: '/personCenter',
        component: () => import('@/views/personCenter'),
        meta: { title: '个人中心' },
      },
      {
        // 工作统计
        name: 'WorkStats',
        path: '/work-stats',
        component: () => import('@/views/WorkStats'),
        meta: { title: '工作统计' },
      },
    ],
  },
  {
    name: 'assessment',
    path: '/assessment',
    component: () => import('@/views/assessment/AssessmentList'),
    meta: { title: '考核任务' },
  },
  {
    name: 'assessmentRecord',
    path: '/assessment/record',
    component: () => import('@/views/assessment/AssessmentRecord'),
    meta: { title: '考核登记' },
  },
  {
    name: 'assessmentDetail',
    path: '/assessment/detail',
    component: () => import('@/views/assessment/TaskDetail'),
    meta: { title: '考核任务详情' },
  },
  {
    name: 'violationRecord',
    path: '/assessment/violation',
    component: () => import('@/views/assessment/ViolationRecord'),
    meta: { title: '登记违规' },
  },
  {
    name: 'problem',
    path: '/problem',
    component: () => import('@/views/ProblemReporting'),
    meta: { title: '问题上报' },
  },
  {
    name: 'smallAd',
    path: '/smallAd',
    component: () => import('@/views/smallAd'),
    meta: { title: '小广告' },
  },
  {
    name: 'ToDoTasks',
    path: '/ToDoTasks',
    component: () => import('@/views/ToDoTasks'),
    meta: { title: '待办任务' },
  },
  {
    name: 'ToDoTasksDetail',
    path: '/ToDoTasksDetail',
    component: () => import('@/views/ToDoTasks/ToDoTasksDetail'),
    meta: { title: '待办任务' },
  },
  {
    name: 'PendingTasks',
    path: '/PendingTasks',
    component: () => import('@/views/PendingTasks'),
    meta: { title: '待处置任务' },
  },
  {
    name: 'PendingTasksDetail',
    path: '/PendingTasksDetail',
    component: () => import('@/views/PendingTasks/PendingTasksDetail'),
    meta: { title: '待处置任务' },
  },
  {
    name: 'ApplyForReturn',
    path: '/ApplyForReturn',
    component: () => import('@/views/PendingTasks/PendingTasksDetail/childPage/ApplyForReturn'),
    meta: { title: '申请退回' },
  },
  {
    name: 'ApplyForExtension',
    path: '/ApplyForExtension',
    component: () => import('@/views/PendingTasks/PendingTasksDetail/childPage/ApplyForExtension'),
    meta: { title: '申请延期' },
  },
  {
    name: 'clockIn',
    path: '/clockIn',
    component: () => import('@/views/clockIn'),
    meta: { title: '考勤打卡' },
  },
  {
    name: 'IdiomaticExpressions',
    path: '/IdiomaticExpressions',
    component: () => import('@/views/IdiomaticExpressions'),
    meta: { title: '惯用语设置' },
  },
  {
    name: 'drafts',
    path: '/drafts',
    component: () => import('@/views/drafts'),
    meta: { title: '草稿箱' },
  },
  {
    name: 'DraftDetail',
    path: '/DraftDetail',
    component: () => import('@/views/drafts/DraftDetail'),
    meta: { title: '问题上报' },
  },
  {
    name: 'SpecialRectification',
    path: '/SpecialRectification',
    component: () => import('@/views/SpecialRectification'),
    meta: { title: '专项整治' },
  },
  {
    name: 'SpecialRectificationDetail',
    path: '/SpecialRectificationDetail',
    component: () => import('@/views/SpecialRectification/Detail'),
    meta: { title: '专项整治' },
  },
  {
    name: 'SpecialRectificationSubmit',
    path: '/SpecialRectificationSubmit',
    component: () => import('@/views/SpecialRectification/Submit'),
    meta: { title: '专项整治' },
  },
  {
    name: 'SpecialRectificationSubmitZz',
    path: '/SpecialRectificationSubmitZz',
    component: () => import('@/views/SpecialRectification/SubmitZz'),
    meta: { title: '专项整治' },
  },
  {
    name: 'CompletedTasks',
    path: '/CompletedTasks',
    component: () => import('@/views/CompletedTasks'),
    meta: { title: '已办任务' },
  },
]

const router = new VueRouter({
  mode: 'history',
  base: '/jiandumobile',
  routes,
})

// 在导出路由实例前添加守卫逻辑
router.beforeEach((to, from, next) => {
  // 排除登录页本身
  if (to.path !== '/login') {
    // 检查是否存在token（假设token存储在localStorage）
    const token = getToken()
    if (!token) {
      next('/login') // 无token则跳转登录页
      return
    }
  }
  next() // 放行其他情况
})

export default router
