<!--基本信息-->
<template>
  <div class="basic-info">
    <!-- 任务编号 -->
    <div class="info-item special">
      <div class="item-label">
        <van-tag type="danger" plain class="dangerBtn" v-if="taskDetail.urgent && taskDetail.urgent == 1">紧急</van-tag>
        <span class="num-label">{{ taskDetail.id || '暂无数据' }}</span>
      </div>
    </div>

    <!-- 基本信息列表 -->
    <div class="info-list">
      <div class="info-item">
        <div class="item-label">问题来源</div>
        <div class="item-value">{{ taskDetail.source || '暂无数据' }}</div>
      </div>
      <div class="info-item">
        <div class="item-label">问题类型</div>
        <div class="item-value">{{ taskDetail.type || '暂无数据' }}</div>
      </div>
      <div class="info-item">
        <div class="item-label">大类名称</div>
        <div class="item-value">{{ taskDetail.mainCategory || '暂无数据' }}</div>
      </div>
      <div class="info-item">
        <div class="item-label">小类名称</div>
        <div class="item-value">{{ taskDetail.subCategory || '暂无数据' }}</div>
      </div>
      <div class="info-item">
        <div class="item-label">立案标准</div>
        <div class="item-value">{{ taskDetail.labz || '暂无数据' }}</div>
      </div>
      <div class="info-item">
        <div class="item-label">所属区县</div>
        <div class="item-value">{{ taskDetail.community || '暂无数据' }}</div>
      </div>
      <div class="info-item">
        <div class="item-label">所属街道</div>
        <div class="item-value">{{ taskDetail.street || '暂无数据' }}</div>
      </div>
      <div class="info-item">
        <div class="item-label">事发地址</div>
        <div class="item-value">{{ taskDetail.address || '暂无数据' }}</div>
      </div>
      <div class="info-item">
        <div class="item-label">问题描述</div>
        <div class="item-value">{{ taskDetail.description || '暂无数据' }}</div>
      </div>
    </div>

    <!-- 地图位置 -->
    <div class="map-location">
      <div class="map-container" @click="showMapDialog = true">
        <img src="@/assets/ToDoTasks/map-placeholder.png" alt="地图位置" class="map-image" />
        <div class="map-marker">
          <van-icon name="location-o" size="24" color="#1989fa" />
          <div class="marker-label">点击查看点位</div>
        </div>
      </div>
    </div>

    <!-- 附件图片区域 -->
    <div class="attachment-area">
      <div class="attachment-label">现场图片</div>
      <div class="upload-placeholder">
        <vantFileUpload v-model="fileStrLocal" :file-type="['jpg', 'png']" @change="updateFileStr"></vantFileUpload>
      </div>
    </div>

    <!-- 备注区域 -->
    <div class="remark-area">
      <div class="remark-label-container">
        <div class="remark-label">处置结果</div>
        <idioms-selector @select="selectIdiom">选择惯用语</idioms-selector>
      </div>
      <van-field
          v-model="opinionLocal"
          type="textarea"
          placeholder="请输入说明内容"
          rows="1"
          autosize
          @input="updateOpinion"
      />
    </div>

    <div class="remark-area">
      <div class="remark-label-container">
        <div class="remark-label">联系电话</div>
      </div>
      <van-field
          v-model="contactPhoneLocal"
          type="textarea"
          placeholder="请输入联系电话"
          rows="1"
          autosize
          @input="updatecontactPhone"
      />
    </div>

    <!-- 地图弹窗 -->
    <van-popup
        v-model="showMapDialog"
        position="bottom"
        :style="{ height: '80%', width: '100%' }"
    >
      <div class="map-dialog">
        <Map
            v-if="showMapDialog"
            ref="mapComponent"
            @locationSelected="handleLocationSelected"
            :coordinates="coordinates"
        />
        <div class="map-dialog-footer">
          <van-button type="primary" block @click="confirmLocation">确认位置</van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import IdiomsSelector from '@/components/IdiomsSelector';
import Map from '@/components/map/index.vue'
import vantFileUpload from "@/components/vant-file-upload";
export default {
  name: 'BasicInformation',
  components: {
    IdiomsSelector,
    Map,
    vantFileUpload
  },
  props: {
    taskDetail: {
      type: Object,
      default: () => ({})
    },
    opinion: {
      type: String,
      default: ''
    },
    contactPhone: {
      type: String,
      default: ''
    },
    fileStr: {
      type: String,
      default: ''
    },
    coordinates: {
      type: Array,
      default: () => ([])
    }
  },
  data() {
    return {
      showMapDialog: false,  // 新增弹窗控制状态
      opinionLocal: this.opinion,
      contactPhoneLocal: this.contactPhone,
      fileStrLocal: this.fileStr,
      areaOptions: []
    }
  },
  mounted() {
    this.getDictList()
  },
  watch: {
    opinion(val) {
      this.opinionLocal = val;
    },
    contactPhone(val) {
      this.contactPhoneLocal = val;
    },
    fileStr(val) {
      this.fileStrLocal = val
    }
  },
  methods: {
    getDictList() {
      this.getDicts('county').then((response) => {
        this.areaOptions = response.data.map(item => ({
          label: item.dictLabel,
          value: item.dictValue
        }))
      })
    },
    // 上传图片后回调
    onUpload(file) {
      this.$emit('upload', file);
    },
    updateOpinion(val) {
      this.$emit('update:opinion', val);
    },
    updatecontactPhone(val) {
      this.$emit('update:contactPhone', val);
    },
    updateFileStr(val) {
      this.$emit('update:fileStr', val);
    },
    // 选择惯用语
    selectIdiom(content) {
      this.opinionLocal = content;
      this.updateOpinion(content);
    },

    handleLocationSelected(e) {
      this.taskDetail.street = e.addressComponent.road
      this.taskDetail.community = this.areaOptions.find(item => item.label == e.addressComponent.county).value?this.areaOptions.find(item => item.label == e.addressComponent.county).value:""
      this.taskDetail.address = e.formatted_address
    },
    confirmLocation() {
      this.showMapDialog = false
    }
  }
}
</script>

<style lang="scss" scoped>
// 基本信息样式
.basic-info {
  .info-item {
    background-color: #fff;
    padding: 12px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    &.special {
      padding: 15px;

      .item-label {
        display: flex;
        align-items: center;

        .dangerBtn {
          width: 55px;
          height: 26px;
          text-align: center;
          line-height: 26px;
          border-radius: 5px;
          background: #FFE3E3;
          font-family: Source Han Sans, Source Han Sans;
          font-weight: 400;
          font-size: 12px;
          color: #FC4242;
          font-style: normal;
          text-transform: none;
          margin-right: 10px;
        }

        .num-label {
          color: #333;
          font-weight: 500;
        }
      }
    }

    .item-label {
      color: #666;
      font-size: 15px;
    }

    .item-value {
      color: #333;
      font-size: 15px;
      text-align: left;
      flex: 0.95;
    }
  }
}

.info-list {
  background-color: #fff;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

// 地图位置
.map-location {
  background-color: #fff;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  .map-title {
    font-size: 15px;
    color: #333;
    margin-bottom: 10px;
  }

  .map-container {
    position: relative;
    width: 100%;
    height: 180px;
    border-radius: 8px;
    overflow: hidden;

    .map-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .map-marker {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      display: flex;
      flex-direction: column;
      align-items: center;

      .marker-label {
        font-size: 12px;
        color: #333;
        background-color: rgba(255, 255, 255, 0.8);
        padding: 2px 6px;
        border-radius: 4px;
        margin-top: 4px;
      }
    }
  }
}

// 底部问答区域
.bottom-qa {
  background-color: #fff;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  .qa-title {
    font-size: 15px;
    color: #333;
    margin-bottom: 10px;
  }

  .qa-options {
    display: flex;
    justify-content: flex-end;
  }
}

// 附件区域
.attachment-area {
  background-color: #fff;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  .attachment-label {
    font-size: 15px;
    color: #333;
    margin-bottom: 10px;
  }

  .upload-placeholder {
    width: fit-content;
    height: fit-content;

    .upload-btn {
      width: 100%;
      height: 100%;
      background-color: #f5f7fa;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 8px;
      border: 1px dashed #ddd;
    }
  }
}

// 备注区域
.remark-area {
  background-color: #fff;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  .remark-label-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;

    .remark-label {
      font-size: 15px;
      color: #333;
    }
  }
}

// 新增地图弹窗样式
.map-dialog {
  height: 100%;
  display: flex;
  flex-direction: column;

  &-footer {
    padding: 10px;
    background: #fff;
    box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
  }
}

// 调整地图容器高度
.MapContainer {
  height: 0;  // 隐藏底部固定地图
}
</style>
