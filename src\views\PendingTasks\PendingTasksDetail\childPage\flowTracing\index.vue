<!--流程追踪-->
<template>
  <div class="process-tracking">
    <div class="timeline">
      <div
          class="timeline-item"
          v-for="(item, index) in processHistory"
          :key="index"
      >
        <div class="timeline-left">
          <div class="timeline-dot" :class="{ active: true }"></div>
          <div class="timeline-line" v-if="index !== processHistory.length - 1"></div>
        </div>
        <div class="timeline-content">
          <div class="timeline-header">
            <div class="timeline-header-left">
              <span class="timeline-time">{{ item.name }}</span>
              <span class="timeline-time">{{ item.time }}</span>
            </div>
            <span class="timeline-location">{{ item.departName }}</span>
          </div>
          <div class="timeline-detail" v-for="(obj,j) in item.list">
            <p>执行的操作：{{ obj.content }}</p>
            <p>接收人：{{ obj.person }}</p>
            <p>批转意见：</p>
            <p>{{ obj.opinion }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {getBljg} from "@/api/common";

export default {
  name: 'FlowTracing',
  data() {
    return {
      processHistory: []
    }
  },
  mounted() {
    this.getProcessHistory()
  },
  methods: {
    getProcessHistory() {
      getBljg({eventId: this.$route.query.id}).then(res => {
        this.processHistory = res.data.map(item => ({
          name: this.getNodeType(item.type),
          time: item.time,
          departName: item.departName,
          list: item.list.map(obj => ({
            content: obj.operateName,
            person: obj.operatePerson,
            opinion: obj.opinion?obj.opinion:null,
          }))
        })).filter(item => item.departName)
      })
    },
    getNodeType(number) {
      const typeMap = {
        1: '上报',
        2: '立案',
        3: '派遣',
        4: '处置',
        5: '核查',
        6: '结案'
      }
      return typeMap[number] || ''
    },
  }
}
</script>

<style lang="scss" scoped>
// 流程跟踪样式
.process-tracking {
  padding: 15px;
  background: white;
  .timeline {
    padding: 10px 0;

    .timeline-item {
      display: flex;
      margin-bottom: 20px;

      .timeline-left {
        width: 20px;
        position: relative;

        .timeline-dot {
          width: 10px;
          height: 10px;
          border-radius: 50%;
          background-color: #ccc;
          margin: 6px auto;

          &.active {
            background-color: #1989fa;
          }
        }

        .timeline-line {
          position: absolute;
          top: 16px;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 1px;
          background-color: #eee;
        }
      }

      .timeline-content {
        flex: 1;
        padding-left: 10px;

        .timeline-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;

          .timeline-header-left {
            display: flex;
            justify-content: flex-start;
            align-items: center;

            .timeline-time {
              font-size: 14px;
              color: #1989fa;
              margin-right: 10px;
            }
          }

          .timeline-location {
            font-size: 14px;
            color: #666;
          }
        }

        .timeline-detail {
          background-color: #f2f6fc;
          border-radius: 8px;
          padding: 12px;
          font-size: 14px;
          color: #666;
          line-height: 1.5;
          margin-bottom: 10px;
          p {
            margin: 4px 0;
          }
        }
      }
    }
  }
}
</style>
