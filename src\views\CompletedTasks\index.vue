<!-- 已办任务 -->
<template>
  <div class="completed-tasks-page">
    <!-- 导航栏 -->
    <van-nav-bar
      title="已办任务"
      left-arrow
      fixed
      @click-left="onClickLeft"
    >
    </van-nav-bar>

    <!-- 标签切换 -->
    <div class="tab-fixed">
      <TabSwitch
        v-model="activeTab"
        :tabs="tabOptions"
        @change="handleTabChange"
      />
    </div>

    <!-- 指标统计区域 -->
    <div class="stats-container">
      <div class="index-List">
        <div
          class="index-List-item"
          :class="{ 'active': selectedStatIndex === i }"
          v-for="(item, i) in statsData"
          :key="i"
          @click="onSelectStat(i)"
        >
          <div class="index-List-item-number" :class="{'highlight': selectedStatIndex === i}">{{item.name}}</div>
          <div class="index-List-item-text" :class="{'text-highlight': selectedStatIndex === i}">{{item.value}}</div>
        </div>
      </div>
      <div class="index-base"></div>
    </div>

    <!-- 任务列表 -->
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list
        v-model="loading"
        :finished="finished"
        finished-text="没有更多了"
        :immediate-check="false"
        @load="onLoad"
        style="position: relative;top: 89px;margin-bottom: 45px;"
      >
        <div class="todo-list">
          <van-cell-group v-for="(item, index) in taskList" :key="index">
            <van-cell class="todo-item" @click="getTaskDetail(item)">
              <!-- 主要内容 -->
              <div class="todo-content">
                <!-- 来源信息 -->
                <div class="source-info">
                  <div class="source-info-left">
                    <!-- 图标 -->
                    <div class="source-icon">
                      <van-icon :name="titleIcon" size="30" />
                    </div>
                    <span class="source-label">问题来源：</span>
                    <span class="source-value">{{item.source}}</span>
                  </div>
                </div>

                <div class="todo-container">
                  <div class="todo-container-left">
                    <van-image width="68" height="68" :src="item.image" fit="cover" radius="5"/>
                  </div>
                  <div class="todo-container-right">
                    <!-- 标题 -->
                    <div class="todo-title">
                      {{ item.title }}
                    </div>

                    <!-- 时间信息 -->
                    <div class="todo-info">
                      <span class="time">{{ item.time }}</span>
                    </div>

                    <!-- 内容 -->
                    <div class="todo-title">
                      {{ item.content }}
                    </div>
                  </div>
                </div>
              </div>
            </van-cell>
          </van-cell-group>
        </div>
      </van-list>
    </van-pull-refresh>
  </div>
</template>

<script>
import TabSwitch from "@/components/TabSwitch";
import {getjdyYbrwTJ, getjdyYbrwList, getzybmYbrwTj, getzybmYbrwList} from "@/api/common";

export default {
  name: 'CompletedTasks',
  components: {
    TabSwitch
  },
  data() {
    return {
      // 当前选中的标签
      activeTab: '1',
      // 选中的指标索引
      selectedStatIndex: 0,
      // 标签选项
      tabOptions: [
        { label: '本日', value: '1' },
        { label: '本周', value: '3' },
        { label: '本月', value: '2' },
        { label: '历史', value: '4' },
      ],
      // 统计数据
      statsData: [
        { name: '0', value: '上报数', code: 1 },
        { name: '0', value: '紧急数', code: 2 },
        { name: '0', value: '核实数', code: 3 },
        { name: '0', value: '核查数', code: 4 }
      ],
      // 任务列表
      taskList: [],
      titleIcon: require('@/assets/ToDoTasks/square.png'),
      // 查询参数
      queryParams: {
        pageSize: 10,
        pageNum: 1
      },
      // 下拉刷新相关
      refreshing: false,
      // 上拉加载相关
      loading: false,
      finished: false,
      // 总数据量
      total: 0,
      // 添加 loading 状态
      isLoading: false,
      // 字典数据
      sourceOptions: []
    }
  },
  mounted() {
    this.init();
    // 确保初始滚动位置正确
    this.resetScrollPosition();
  },
  activated() {
    // 在页面被缓存后重新激活时重置滚动位置
    this.resetScrollPosition();
  },
  methods: {
    // 重置滚动位置到顶部
    resetScrollPosition() {
      window.scrollTo(0, 0);
    },

    async init() {
      await this.getDictsList();
      await this.getStatsData();
      // 默认选择第一个指标作为筛选条件
      this.queryParams.eventType = this.statsData[0].code;
      await this.getTaskList();
      // 数据加载完成后，确保滚动位置正确
      this.$nextTick(() => {
        this.resetScrollPosition();
      });
    },

    // 获取字典数据
    async getDictsList() {
      // 问题来源
      return new Promise((resolve) => {
        this.getDicts('zhcg_wtly').then((response) => {
          this.sourceOptions = response.data.map(item => ({
            label: item.dictLabel,
            value: item.dictValue
          }));
          resolve();
        });
      });
    },

    // 获取统计数据
    async getStatsData() {
      const res = this.user.roleType == 1?await getjdyYbrwTJ(this.activeTab):await getzybmYbrwTj(this.activeTab)
      if (res.code == 200) {
        const data = res.data
        this.statsData = this.user.roleType == 1?[
          { name: data.reportCount, value: '上报数', code: 1 },
          { name: data.urgencyCount, value: '紧急数', code: 2 },
          { name: data.hesCount, value: '核实数', code: 3 },
          { name: data.hecCount, value: '核查数', code: 4 }
        ]:[
          { name: data.czCount, value: '处置数', code: 1 },
          { name: data.asczCount, value: '按时处置数', code: 2 },
          { name: data.cqczCount, value: '超期处置数', code: 3 },
          { name: data.backCount, value: '返工数', code: 4 },
        ]
      }
    },

    // 获取任务列表
    async getTaskList() {
      if (this.isLoading) return;

      try {
        this.isLoading = true;

        // 显示加载提示
        this.$toast.loading({
          message: '加载中...',
          forbidClick: true,
          duration: 0
        });

        // 获取列表数据
        const res = this.user.roleType == 1?await getjdyYbrwList(this.queryParams,this.activeTab):await getzybmYbrwList(this.queryParams,this.activeTab)
        console.log(res,"getjdyYbrwList");
        this.total = res.data.total;

        // 如果是刷新，清空列表
        if (this.refreshing) {
          this.taskList = [];
        }

        // 追加数据
        this.taskList.push(...res.data.list.map(item => ({
          id: item.id,
          source: this.getDictText('sourceOptions', item.source),
          title: item.type4id,
          time: item.createtime,
          content: item.eventdesc,
          image: item.fileStr ? this.$getImageUrl(item.fileStr) : ''
        })));

        // 判断是否加载完成
        this.finished = this.taskList.length >= this.total;

        // 加载状态结束
        this.loading = false;

        // 刷新状态结束
        if (this.refreshing) {
          this.refreshing = false;
          // 刷新后重置滚动位置
          this.$nextTick(() => {
            this.resetScrollPosition();
          });
        }

        // 关闭加载提示
        this.$toast.clear();

      } catch (error) {
        console.error('获取列表失败:', error);
        this.$toast.fail('获取列表失败');
        this.$toast.clear();
      } finally {
        this.isLoading = false;
      }
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        '4': '已核实',
        '17': '已核查',
        '18': '已终结'
        // 根据实际需要添加更多状态
      };
      return statusMap[status] || '已处理';
    },

    // 下拉刷新
    async onRefresh() {
      this.queryParams.pageNum = 1;
      this.finished = false;
      await this.getStatsData();
      await this.getTaskList();
    },

    // 上拉加载更多
    onLoad() {
      // 在此处检查，避免首次加载时自动触发
      if (this.taskList.length > 0) {
        this.queryParams.pageNum += 1;
        this.getTaskList();
      } else {
        // 如果是首次加载，不增加页码
        this.loading = false;
      }
    },

    // 标签切换
    handleTabChange(value) {
      this.activeTab = value;
      this.queryParams.pageNum = 1;
      this.taskList = [];
      this.finished = false;
      this.getStatsData().then(() => {
        // 重置为默认选中第一个指标
        this.selectedStatIndex = 0;
        this.queryParams.eventType = this.statsData[0].code;
        this.getTaskList();
        // 切换标签后重置滚动位置
        this.$nextTick(() => {
          this.resetScrollPosition();
        });
      });
    },

    // 任务详情
    getTaskDetail(item) {
      // this.$router.push({
      //   name: 'CompletedTasksDetail',
      //   query: {
      //     id: item.id
      //   }
      // });
    },

    // 返回上一页
    onClickLeft() {
      this.$router.go(-1);
    },

    // 选择指标
    onSelectStat(index) {
      this.selectedStatIndex = index;
      const selectedStat = this.statsData[index];
      this.$toast(`已选择: ${selectedStat.value}`);

      // 这里可以根据实际需求添加其他处理逻辑
      // 例如根据选中的指标过滤任务列表
      this.filterTaskListByStat(selectedStat.code);
    },

    // 根据选中的指标过滤任务列表
    filterTaskListByStat(statCode) {
      // 这里可以根据实际需求实现过滤逻辑
      // 例如：重新请求接口，传入指标代码
      this.queryParams.eventType = statCode;
      this.queryParams.pageNum = 1;
      this.taskList = [];
      this.finished = false;
      this.getTaskList();
    }
  }
}
</script>

<style lang="scss" scoped>
.completed-tasks-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-top: 130px; // 为固定定位的导航栏、标签栏和统计区域预留空间
}

// 标签栏固定定位
.tab-fixed {
  position: fixed;
  top: 46px; // 导航栏高度
  left: 0;
  right: 0;
  z-index: 99;
  background-color: #fff;
}

// 统计区域样式
.stats-container {
  height: 131px;
  position: fixed;
  top: 90px; // 导航栏+标签栏高度
  left: 0;
  right: 0;
  z-index: 98;
  padding: 17px;
  background: #f5f5f5;

  .index-List {
    border-radius: 8px;
    display: flex;
    background-color: white;

    .index-List-item {
      flex: 1;
      height: 93px;
      display: flex;
      flex-direction: column;
      justify-content: space-evenly;
      align-items: center;
      position: relative;
      cursor: pointer;
      transition: all 0.3s ease;
      border-bottom: 1px solid #eee;

      &.active {
        .text-highlight {
          color: #FF9800;
          font-weight: 500;
        }
      }

      &:active {
        opacity: 0.7;
      }

      .index-List-item-number {
        font-family: Roboto, Roboto;
        font-weight: 500;
        font-size: 25px;
        color: #121212;
        text-align: center;
        transition: all 0.3s;

        &.highlight {
          width: 30px;
          height: 30px;
          background-color: #FFC061;
          color: #fff;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 5px;
        }
      }

      .index-List-item-text {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 14.5px;
        color: #6B7280;
        text-align: center;
        transition: color 0.3s;

        &.text-highlight {
          color: #FF9800;
        }
      }
    }
  }

  .index-base {
    height: 23px;
    background: url("~@/assets/home/<USER>") no-repeat;
    background-size: cover;
    position: relative;
    bottom: 15px;
  }
}

// 任务列表样式
.todo-list {
  .van-cell-group {
    margin-bottom: 12px;
    border-radius: 8px;
    overflow: hidden;
  }
}

// 任务项样式
.todo-item {
  ::v-deep .van-cell__value {
    flex: 1;
  }

  .source-icon {
    width: 32px;
    height: 32px;
    background-color: #e8f3ff;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    margin-left: 16px;
  }

  .todo-content {
    padding: 0 0 5px 0;
    flex: 1;

    .source-info {
      width: 100%;
      height: 49px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 14px;
      color: #666;
      border-bottom: 1px solid #DADFE8;
      margin-bottom: 8px;

      .source-info-left {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .source-label,.source-value {
          font-family: Source Han Sans, Source Han Sans;
          font-weight: 700;
          font-size: 19px;
          color: #333333;
          line-height: 21px;
          text-align: left;
          font-style: normal;
        }
      }
    }

    .todo-container {
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
      .todo-container-left {
        margin: 4px 16px 0 16px;
      }
      .todo-container-right {
        .todo-title {
          font-family: Source Han Sans, Source Han Sans;
          font-weight: 400;
          font-size: 15px;
          color: #333333;
          text-align: left;
          font-style: normal;
        }

        .todo-info {
          font-size: 13px;
          color: #999;
          margin: 4px 0;
        }

        .task-status {
          font-size: 13px;
          margin-top: 4px;
          margin-bottom: 13px;

          .status-tag {
            display: inline-block;
            padding: 5px 8px;
            background: #E8F3FF;
            color: #1989fa;
            border-radius: 5px;
          }

          .urgent-tag {
            display: inline-block;
            padding: 2px 6px;
            background: #FFE3E3;
            color: #FC4242;
            margin-left: 8px;
            font-size: 12px;
            border-radius: 5px;
          }
        }
      }
    }
  }
}

::v-deep .van-cell {
  padding: unset;
}

// 修改下拉刷新和加载更多的样式
.van-pull-refresh {
  min-height: calc(100vh - 130px);
}

.van-list {
  min-height: 100%;
}

// 优化滚动区域
.van-pull-refresh__track {
  min-height: 100%;
}
</style>
