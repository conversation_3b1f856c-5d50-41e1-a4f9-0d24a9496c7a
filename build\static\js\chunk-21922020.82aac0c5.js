(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-21922020"],{"348a":function(t,e,a){},"43a3":function(t,e,a){"use strict";a("348a")},9237:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t._self._c;return e("div",{staticClass:"task-detail"},[e("van-nav-bar",{staticStyle:{position:"fixed"},attrs:{title:"任务详情","left-arrow":"",fixed:""},on:{"click-left":t.onClickLeft}}),e("div",{staticClass:"content"},[e("van-cell-group",{attrs:{border:!1}},[e("van-cell",{attrs:{title:"路段",value:t.detail.roadName,"title-class":"label","value-class":"value"}}),e("van-cell",{attrs:{title:"大类",value:t.detail.bigIndex,"title-class":"label","value-class":"value"}}),e("van-cell",{attrs:{title:"小类",value:t.detail.smallIndex,"title-class":"label","value-class":"value"}}),e("van-cell",{attrs:{title:"问题描述",value:t.detail.inText,"title-class":"label","value-class":"value"}})],1),e("div",{staticClass:"photo-section"},[e("div",{staticClass:"section-title label"},[t._v("照片")]),t.detail.images.length>0?e("div",{staticClass:"photo-list"},t._l(t.detail.images,(function(t,a){return e("van-image",{key:a,attrs:{src:t,width:"100",height:"100",radius:"4",fit:"cover"}})})),1):t._e(),0===t.detail.images.length?e("van-empty",{attrs:{"image-size":"24",description:"暂无照片"}}):t._e()],1),e("div",{staticClass:"bottom-info"},[e("van-button",{attrs:{type:"info",block:""},on:{click:t.showPopup}},[t._v("已登记"+t._s(t.totalRecords)+"条，点击切换")])],1)],1),e("van-popup",{style:{maxHeight:"60%"},attrs:{position:"bottom",round:""},model:{value:t.showRecordList,callback:function(e){t.showRecordList=e},expression:"showRecordList"}},[e("div",{staticClass:"popup-content"},[e("van-cell-group",t._l(t.records,(function(a,s){return e("van-cell",{key:s,attrs:{label:`${a.bigIndex} - ${a.smallIndex}`,title:""+a.roadName,"is-link":""},on:{click:function(e){return t.switchRecord(s)}}})})),1)],1)])],1)},i=[],l=(a("13d5"),a("e9f5"),a("ab43"),a("9485"),a("a0f3")),n={name:"TaskDetail",data(){return{detail:{roadName:"",bigIndex:"",smallIndex:"",inText:"",images:[]},records:[],showRecordList:!1,currentRoadIndex:0,currentRecordIndex:0}},computed:{totalRecords(){return this.records.length}},created(){const t=this.$route.query.id;t&&this.getDetail(t)},methods:{onClickLeft(){this.$router.back()},async getDetail(t){try{const e=await Object(l["b"])({id:t});if(200===e.code){this.records=e.data.roadAssessMinorVOS.reduce((t,e)=>{var a;const s=(null===(a=e.roadCheckVOS)||void 0===a?void 0:a.map(t=>({...t,roadName:e.roadName})))||[];return[...t,...s]},[]);const t=this.records[0];t&&(this.detail={roadName:t.roadName,bigIndex:t.bigIndex,smallIndex:t.smallIndex,inText:t.inText,images:t.filePath?t.filePath.split(","):[]})}}catch(e){console.error("获取详情失败:",e),this.$toast("获取详情失败")}},showPopup(){this.showRecordList=!0},switchRecord(t){const e=this.records[t];this.detail={roadName:e.roadName,bigIndex:e.bigIndex,smallIndex:e.smallIndex,inText:e.inText,images:e.filePath?e.filePath.split(","):[]},this.currentRecordIndex=t,this.showRecordList=!1}}},r=n,o=(a("43a3"),a("2877")),c=Object(o["a"])(r,s,i,!1,null,"e4c0785a",null);e["default"]=c.exports},a0f3:function(t,e,a){"use strict";a.d(e,"c",(function(){return i})),a.d(e,"b",(function(){return l})),a.d(e,"d",(function(){return n})),a.d(e,"e",(function(){return r})),a.d(e,"a",(function(){return o}));var s=a("4020");function i(t){return Object(s["a"])({url:"/road/assess/main/list",method:"get",params:t})}function l(t){return Object(s["a"])({url:"/road/assess/main/select",method:"get",params:t})}function n(t){return Object(s["a"])({url:"/road/assess/check/getsubtract",method:"get",params:t})}function r(t){return Object(s["a"])({url:"/road/assess/startcheck/update",method:"put",data:t})}function o(t){return Object(s["a"])({url:"/road/assess/check/add",method:"post",data:t})}}}]);