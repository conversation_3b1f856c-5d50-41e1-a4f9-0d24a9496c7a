(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3f2107a7"],{"207e":function(t,i,e){"use strict";var s=function(){var t=this,i=t._self._c;return i("div",{staticClass:"vant-upload-file"},[i("van-uploader",{ref:"upload",attrs:{"max-count":t.limit,accept:t.accept,disabled:t.$attrs.disabled,"max-size":1024*t.fileSize*1024,"before-read":t.handleBeforeUpload,"after-read":t.handleAfterRead,"upload-text":t.uploadText,"upload-icon":t.uploadIcon,capture:"camera"},scopedSlots:t._u([{key:"default",fn:function(){return[t._t("default",(function(){return[i("div",{staticClass:"upload-icon-wrapper"},[i("van-icon",{staticClass:"upload-icon",attrs:{name:"photograph"}})],1)]}))]},proxy:!0}],null,!0),model:{value:t.internalFileList,callback:function(i){t.internalFileList=i},expression:"internalFileList"}}),t.showFileList&&t.fileList.length>0?i("div",{staticClass:"file-list-container"},[i("div",{staticClass:"file-list-title"},[t._v("已上传文件")]),t.$attrs.disabled?[i("div",{staticClass:"file-list-wrapper"},[t._l(t.fileList,(function(e,s){return i("div",{key:e.uid||s,staticClass:"file-item disabled"},[i("div",{staticClass:"file-info",on:{click:function(i){return t.handlePreview(e)}}},[i("van-icon",{staticClass:"file-icon",attrs:{name:"description"}}),i("div",{staticClass:"file-name van-ellipsis"},[t._v(t._s(e.name))])],1)])})),t.fileList.length>2?i("van-button",{staticClass:"toggle-button",attrs:{size:"mini",type:"default"},on:{click:t.toggleExpand}},[t._v(" "+t._s(t.isExpanded?"收起":"展开")+" ")]):t._e()],2)]:t._l(t.fileList,(function(e,s){return i("div",{key:e.uid||s,staticClass:"file-item"},[i("div",{staticClass:"file-info",on:{click:function(i){return t.handlePreview(e)}}},[i("van-icon",{staticClass:"file-icon",attrs:{name:"description"}}),i("div",{staticClass:"file-name van-ellipsis"},[t._v(t._s(e.name))])],1),i("div",{staticClass:"file-actions"},[i("van-icon",{staticClass:"delete-icon",attrs:{name:"delete"},on:{click:function(i){return t.handleFileDelete(s)}}})],1)])}))],2):t._e(),t.showTip?i("div",{staticClass:"upload-tip"},[i("p",[i("span",[t._v("支持格式："+t._s(t.fileTypeText))]),t.fileSize?i("span",[t._v("，大小不超过 "+t._s(t.fileSize)+"MB")]):t._e()])]):t._e()],1)},a=[],o=(e("e9f5"),e("910d"),e("7d54"),e("ab43"),e("a732"),e("0a5a")),n=e("4260"),l={name:"index",props:{limit:{type:Number,default:4},accept:{type:String,default:"image/*"},action:{type:String,default:"/prod-api/sysUploadFile/uploadFile"},autoUpload:{type:Boolean,default:!0},fileList:{type:Array,default:()=>[]},data:{type:Object,default:()=>({})},name:{type:String,default:"multipartFile"},value:[String,Object,Array,Number],fileSize:{type:Number,default:20},fileType:{type:Array,default:()=>["doc","docx","pdf","txt","xls","xlsx","png","jpg","jpeg","gif","mp3","mp4","mov","avi"]},isShowTip:{type:Boolean,default:!0},showFileList:{type:Boolean,default:!0},uploadIcon:{type:String,default:"photograph"},uploadText:{type:String,default:"上传文件"}},data(){return{headers:{Authorization:"Bearer "+Object(o["a"])()},internalFileList:[],isExpanded:!1,uploading:!1}},computed:{showTip(){return!this.$attrs.disabled&&(this.isShowTip&&(this.fileType||this.fileSize))},fileTypeText(){return this.fileType.join("、")}},watch:{fileList:{handler(t){this.internalFileList=t.map(t=>({url:Object(n["c"])(t),name:this.getFileName(t),isImage:/\.(jpeg|jpg|gif|png|webp)$/i.test(t||"")}))},immediate:!0},value:{handler(t){t?(this.fetchFiles(t),this.$emit("change",t)):(this.internalFileList=[],this.$emit("change",""))},immediate:!0},internalFileList:{handler(t){if(t&&t.length>0){const i=t.map(t=>t.uid||"").filter(Boolean).join(",");i&&i!==this.value&&(this.$emit("input",i),this.$emit("change",i))}else this.value&&(this.$emit("input",""),this.$emit("change",""))},deep:!0}},created(){this.value&&!this.internalFileList.length&&this.fetchFiles(this.value)},methods:{fetchFiles(t){if(t){const i=t.split(",").filter(Boolean).map(t=>({name:this.getFileName(t),url:Object(n["c"])(t),uid:t,isImage:/\.(jpeg|jpg|gif|png|webp)$/i.test(t||"")}));this.$emit("update:fileList",i),this.internalFileList=i}},handleBeforeUpload(t){if(this.fileType&&this.fileType.length>0){let i="";t.name.lastIndexOf(".")>-1&&(i=t.name.slice(t.name.lastIndexOf(".")+1).toLowerCase());const e=this.fileType.some(e=>t.type.indexOf(e)>-1||!(!i||e.toLowerCase()!==i));if(!e)return this.$toast.fail(`文件格式不正确, 请上传${this.fileType.join("/")}格式文件!`),!1}if(this.fileSize){const i=t.size/1024/1024<this.fileSize;if(!i)return this.$toast.fail(`上传文件大小不能超过 ${this.fileSize}MB!`),!1}return!0},handleAfterRead(t){this.autoUpload?this.uploadFile(t):this.$emit("select",t)},uploadFile(t){this.uploading=!0;const i=new FormData;t.file&&i.append(this.name,t.file),this.data&&Object.keys(this.data).forEach(t=>{i.append(t,this.data[t])}),t.status="uploading",t.message="上传中...",fetch(this.action,{method:"POST",headers:this.headers,body:i}).then(t=>t.json()).then(i=>{if(this.uploading=!1,200===i.code){if(t.status="done",t.message="上传成功",i.data){Object.assign(t,{url:Object(n["c"])(i.data),uid:i.data});const e=this.internalFileList.map(t=>t.uid||"").filter(Boolean).join(",");this.$emit("input",e),this.$emit("change",e)}this.$emit("success",{response:i,file:t,fileList:this.internalFileList})}else t.status="failed",t.message=i.msg||"上传失败",this.$toast.fail("文件上传失败，请重新上传"),this.$emit("error","文件上传失败，请重新上传")}).catch(i=>{this.uploading=!1,t.status="failed",t.message="上传失败",this.$toast.fail("文件上传失败，请重新上传"),this.$emit("error",i.message||"文件上传失败，请重新上传")})},handleFileDelete(t){this.$dialog.confirm({title:"提示",message:"是否确认删除？删除后将无法恢复"}).then(()=>{const i=this.internalFileList[t];this.internalFileList.splice(t,1),this.$emit("remove",i),this.$emit("update:fileList",[...this.internalFileList]),this.$emit("input",this.internalFileList.map(t=>t.uid).join(","))}).catch(()=>{})},handlePreview(t){if(this.$emit("preview",t),t.isImage){const i=this.internalFileList.filter(t=>t.isImage).map(t=>t.url),e=i.indexOf(t.url);this.$imagePreview({images:i,startPosition:e>-1?e:0,closeable:!0})}else t.url&&window.open(t.url)},toggleExpand(){this.isExpanded=!this.isExpanded},getFileName(t){return t?t.lastIndexOf("/")>-1?t.slice(t.lastIndexOf("/")+1).toLowerCase():t:""},submit(){if(this.internalFileList.some(t=>"uploading"===t.status))return void this.$toast("有文件正在上传中，请稍后再试");const t=this.internalFileList.filter(t=>!t.status||"failed"===t.status);0!==t.length?t.forEach(t=>{this.uploadFile(t)}):this.$toast("没有需要上传的文件")},clearFiles(){this.internalFileList=[],this.$emit("update:fileList",[]),this.$emit("input","")}}},c=l,r=(e("58d9"),e("2877")),h=Object(r["a"])(c,s,a,!1,null,"6abab3f2",null);i["a"]=h.exports},"259f":function(t,i,e){},"428a":function(t,i,e){"use strict";e.r(i);var s=function(){var t,i,e=this,s=e._self._c;return s("div",{staticClass:"clock-in-page"},[s("van-nav-bar",{attrs:{title:"考勤打卡","left-arrow":"",fixed:""},on:{"click-left":e.onClickLeft}}),s("div",{staticClass:"head-bg"}),s("div",{staticClass:"user-card"},[s("div",{staticClass:"user-info"},[s("div",{staticClass:"avatar"},[s("van-image",{attrs:{round:"",width:"40",height:"40",src:e.avatar}})],1),s("div",{staticClass:"info"},[s("div",{staticClass:"name"},[e._v(e._s((null===(t=e.user)||void 0===t?void 0:t.nickName)||"未知用户"))]),s("div",{staticClass:"date"},[e._v(e._s(e.currentDate)+" "+e._s(e.currentTime)+" "),s("span",{staticClass:"dept-name"},[e._v(e._s((null===(i=e.user)||void 0===i?void 0:i.deptName)||""))])])])]),s("div",{staticClass:"card-right"},[s("van-image",{attrs:{round:"",width:"30",height:"30",src:e.dateIcon}})],1)]),s("div",{staticClass:"clock-status"},[e.loadingInfo?s("div",{staticClass:"loading-container"},[s("van-loading",{attrs:{color:"#1989fa"}}),s("div",{staticClass:"loading-text"},[e._v("加载打卡信息...")])],1):e.loadError?s("div",{staticClass:"error-container"},[s("div",{staticClass:"error-text"},[e._v("打卡信息加载失败")]),s("van-button",{attrs:{plain:"",type:"info",size:"small"},on:{click:e.retryLoading}},[e._v("重新加载")])],1):[s("div",{staticClass:"timeline"},["none"!==e.clockInStatus?s("div",{staticClass:"timeline-line"}):e._e(),s("div",{staticClass:"timeline-item"},[s("div",{staticClass:"timeline-dot",class:{active:"none"!==e.clockInStatus}}),s("div",{staticClass:"timeline-content"},[s("div",{staticClass:"timeline-title"},[e._v("上班时间 "+e._s(e.workStartTime))]),"none"!==e.clockInStatus?[s("div",{staticClass:"timeline-info"},[s("div",{staticClass:"time-label"},[e._v(" 打卡时间 "+e._s(e.clockInTime)+" "),s("span",{staticClass:"status-tag",class:{"late-tag":e.isLate}},[e._v(" "+e._s(e.isLate?"迟到":"已打卡")+" ")])]),e.signFilePath&&e.signFilePath.length>0?s("div",{staticClass:"location-image"},[s("van-swipe",{attrs:{autoplay:3e3,"indicator-color":"#1989fa","show-indicators":e.signFilePath.length>1},on:{change:e.onSignSwipeChange}},e._l(e.signFilePath,(function(t,i){return s("van-swipe-item",{key:"sign-"+i},[s("van-image",{attrs:{width:"100%",height:"120px",fit:"cover",src:t,radius:"4"},on:{click:function(t){return e.previewImage(e.signFilePath,i)}}})],1)})),1),e.signFilePath.length>1?s("div",{staticClass:"image-counter"},[e._v(" "+e._s(e.signActiveIndex+1)+"/"+e._s(e.signFilePath.length)+" ")]):e._e()],1):s("div",{staticClass:"location-image-empty"},[s("van-empty",{attrs:{description:"暂无图片"}})],1)])]:e._e()],2)]),e.reportRecords&&e.reportRecords.length>0?s("div",{staticClass:"timeline-item"},[s("div",{staticClass:"timeline-dot active"}),s("div",{staticClass:"timeline-content"},[s("div",{staticClass:"timeline-title"},[e._v("位置上报记录")]),e._l(e.reportRecords,(function(t,i){return s("div",{key:"report-"+i,staticClass:"timeline-info"},[s("div",{staticClass:"time-label"},[e._v(" 上报时间 "+e._s(t.reportTime)+" "),s("span",{staticClass:"status-tag report-tag"},[e._v("已上报")])]),t.location?s("div",{staticClass:"report-location"},[e._v(" 位置信息: "+e._s(t.location)+" ")]):e._e()])}))],2)]):e._e(),"out"===e.clockInStatus?s("div",{staticClass:"timeline-item"},[s("div",{staticClass:"timeline-dot",class:{active:"out"===e.clockInStatus}}),s("div",{staticClass:"timeline-content"},[s("div",{staticClass:"timeline-title"},[e._v("下班时间 "+e._s(e.workEndTime))]),[s("div",{staticClass:"timeline-info"},[s("div",{staticClass:"time-label"},[e._v(" 打卡时间 "+e._s(e.clockOutTime)+" "),s("span",{staticClass:"status-tag",class:{"early-tag":e.isEarlyLeave}},[e._v(" "+e._s(e.isEarlyLeave?"早退":"已打卡")+" ")])]),e.outFilePath&&e.outFilePath.length>0?s("div",{staticClass:"location-image"},[s("van-swipe",{attrs:{autoplay:3e3,"indicator-color":"#1989fa","show-indicators":e.outFilePath.length>1},on:{change:e.onOutSwipeChange}},e._l(e.outFilePath,(function(t,i){return s("van-swipe-item",{key:"out-"+i},[s("van-image",{attrs:{width:"100%",height:"120px",fit:"cover",src:t,radius:"4"},on:{click:function(t){return e.previewImage(e.outFilePath,i)}}})],1)})),1),e.outFilePath.length>1?s("div",{staticClass:"image-counter"},[e._v(" "+e._s(e.outActiveIndex+1)+"/"+e._s(e.outFilePath.length)+" ")]):e._e()],1):s("div",{staticClass:"location-image-empty"},[s("van-empty",{attrs:{description:"暂无图片"}})],1)])]],2)]):e._e()]),s("div",{staticClass:"clock-btn-container"},["none"===e.clockInStatus?[s("div",{staticClass:"clock-in-btn",on:{click:e.showClockInPopup}},[s("div",{staticClass:"btn-text"},[e._v("签到")]),s("div",{staticClass:"btn-time"},[e._v(e._s(e.currentTime))])])]:"in"===e.clockInStatus?[s("div",{staticClass:"report-btns"},[s("div",{staticClass:"small-btn report-btn",on:{click:e.handleReport}},[s("div",{staticClass:"btn-text"},[e._v("上报")])]),s("div",{staticClass:"small-btn clock-out-btn",on:{click:e.showClockOutPopup}},[s("div",{staticClass:"btn-text"},[e._v("签退")]),s("div",{staticClass:"btn-time"},[e._v(e._s(e.currentTime))])])])]:e._e()],2)]],2),s("van-popup",{staticClass:"clock-popup",attrs:{round:""},model:{value:e.showPopup,callback:function(t){e.showPopup=t},expression:"showPopup"}},[s("div",{staticClass:"popup-title"},[e._v(e._s(e.isClockIn?"签到":"签退"))]),s("div",{staticClass:"popup-content"},[s("div",{staticClass:"popup-time"},[e._v("打卡时间: "+e._s(e.currentDate)+" "+e._s(e.currentTime))]),s("div",{staticClass:"popup-location"},[s("div",{staticClass:"location-label"},[e._v("请填写打卡备注 (选填)")]),s("van-field",{staticClass:"remark-input",attrs:{placeholder:"请输入"},model:{value:e.clockRemark,callback:function(t){e.clockRemark=t},expression:"clockRemark"}})],1),s("div",{staticClass:"upload-area"},[s("vantFileUpload",{attrs:{"file-type":["jpg","png"]},model:{value:e.filStr,callback:function(t){e.filStr=t},expression:"filStr"}})],1)]),s("div",{staticClass:"popup-buttons"},[s("van-button",{staticClass:"cancel-btn",attrs:{plain:""},on:{click:e.cancelClock}},[e._v("暂不打卡")]),s("van-button",{staticClass:"confirm-btn",attrs:{type:"info",plain:""},on:{click:e.confirmClock}},[e._v(" "+e._s(e.isClockIn?"打卡签到":"打卡签退")+" ")])],1)])],1)},a=[],o=(e("e9f5"),e("910d"),e("ab43"),e("4260")),n=e("207e"),l=e("4020");function c(){return Object(l["a"])({url:"/collectors/late/select",method:"get"})}function r(t){return Object(l["a"])({url:"/collectors/attendance/signOrOut",method:"post",data:t})}function h(t){return Object(l["a"])({url:"/collectors/attendance/select",method:"get",params:t})}var u=e("dfe4"),d={name:"ClockInPage",mixins:[u["a"]],components:{vantFileUpload:n["a"]},data(){return{avatar:e("847b"),dateIcon:e("837c"),currentDate:Object(o["a"])(new Date),currentTime:"",clockInStatus:"none",clockInTime:"",clockOutTime:"",showPopup:!1,clockRemark:"",signFilePath:[],outFilePath:[],signActiveIndex:0,outActiveIndex:0,reportRecords:[],filStr:"",timer:null,isClockIn:!0,workStartTime:"08:30:00",workEndTime:"17:30:00",loading:!1,loadingInfo:!1,loadError:!1,locationError:!1,location:{longitude:"",latitude:""}}},computed:{isLate(){return this.compareTime(this.clockInTime,this.workStartTime)>0},isEarlyLeave(){return this.compareTime(this.clockOutTime,this.workEndTime)<0}},created(){this.getWorkTime(),this.getCurrentClockInfo()},mounted(){this.updateCurrentTime(),this.timer=setInterval(this.updateCurrentTime,1e3),this.getLocation()},beforeDestroy(){this.timer&&clearInterval(this.timer)},methods:{retryLoading(){this.loadError=!1,this.getWorkTime(),this.getCurrentClockInfo(),this.getLocation()},async getWorkTime(){try{const t=await c();200===t.code&&t.data?(this.workStartTime=t.data.workStartTime||this.workStartTime,this.workEndTime=t.data.workEndTime||this.workEndTime,console.log("工作时间获取成功:",this.workStartTime,this.workEndTime)):console.warn("工作时间获取失败，使用默认时间",t)}catch(t){console.error("获取工作时间异常:",t)}},async getCurrentClockInfo(){if(!this.user||!this.user.userId)return console.warn("用户信息未加载，无法获取打卡信息"),void(this.loadError=!0);this.loadingInfo=!0,this.loadError=!1,this.signFilePath=[],this.outFilePath=[],this.signActiveIndex=0,this.outActiveIndex=0,this.reportRecords=[];try{const t=await h({userId:this.user.userId});if(200===t.code){if(t.rows&&t.rows.length>0){const i=t.rows[0];this.clockInTime=i.signTime||"",this.clockOutTime=i.outTime||"",this.clockInStatus=1==i.status?"in":2==i.status?"out":"none",i.signFilePath&&(this.signFilePath=this.getImageArr(i.signFilePath),console.log("上班打卡图片:",this.signFilePath)),i.outFilePath&&(this.outFilePath=this.getImageArr(i.outFilePath),console.log("下班打卡图片:",this.outFilePath)),i.collectorsAttendanceInfoVOList&&i.collectorsAttendanceInfoVOList.length>0&&(this.reportRecords=i.collectorsAttendanceInfoVOList.map(t=>({reportTime:t.time||"",location:`经度: ${Number(t.longitude).toFixed(6)}, 纬度: ${Number(t.latitude).toFixed(6)}`})),console.log("上报记录:",this.reportRecords))}else this.clockInStatus="none";console.log("打卡信息获取成功:",t)}else console.warn("没有获取到打卡信息或返回错误:",t)}catch(t){console.error("获取打卡信息异常:",t),this.loadError=!0}finally{this.loadingInfo=!1}},getImageArr(t){if(!t)return[];try{const i=t.split(",").filter(t=>t&&t.trim());return i.map(t=>Object(o["c"])(t.trim()))}catch(i){return console.error("处理图片地址出错:",i),[]}},getLocation(){this.locationError=!1,navigator.geolocation?(this.$toast.loading({message:"获取位置中...",duration:1e3}),navigator.geolocation.getCurrentPosition(t=>{this.location.longitude=t.coords.longitude,this.location.latitude=t.coords.latitude,console.log("获取位置成功:",this.location),this.$toast.clear()},t=>{console.error("获取位置失败:",t),this.locationError=!0,this.$toast.clear(),this.$toast.fail("获取位置失败，请确保已开启定位权限")},{enableHighAccuracy:!0,timeout:1e4,maximumAge:0})):(this.locationError=!0,this.$toast.fail("您的浏览器不支持获取地理位置"))},updateCurrentTime(){const t=new Date,i=String(t.getHours()).padStart(2,"0"),e=String(t.getMinutes()).padStart(2,"0"),s=String(t.getSeconds()).padStart(2,"0");this.currentTime=`${i}:${e}:${s}`},onClickLeft(){this.$router.go(-1)},showClockInPopup(){"none"===this.clockInStatus?this.location.longitude&&this.location.latitude?(this.clockRemark="",this.filStr="",this.isClockIn=!0,this.showPopup=!0):this.$dialog.confirm({title:"位置信息未获取",message:"需要获取您的位置信息才能打卡，是否重新获取？",confirmButtonText:"重新获取",cancelButtonText:"取消"}).then(()=>{this.getLocation()}).catch(()=>{}):this.$toast("今日已完成签到")},showClockOutPopup(){"out"!==this.clockInStatus?"none"!==this.clockInStatus?this.location.longitude&&this.location.latitude?(this.clockRemark="",this.filStr="",this.isClockIn=!1,this.showPopup=!0):this.$dialog.confirm({title:"位置信息未获取",message:"需要获取您的位置信息才能打卡，是否重新获取？",confirmButtonText:"重新获取",cancelButtonText:"取消"}).then(()=>{this.getLocation()}).catch(()=>{}):this.$toast("请先进行签到"):this.$toast("今日已完成签退")},cancelClock(){this.showPopup=!1,this.clockRemark="",this.filStr=""},async confirmClock(){if(this.loading)return;if(!this.location.longitude||!this.location.latitude)return this.$toast("位置信息获取失败，请重试"),void this.getLocation();if(!this.filStr||0===this.filStr.length)return void this.$toast("请上传打卡照片");this.loading=!0;const t=this.isClockIn?1:2;try{this.$toast.loading({message:this.isClockIn?"签到中...":"签退中...",forbidClick:!0});const i=this.getImageArr(this.filStr),e={nowLongitude:this.location.longitude,nowLatitude:this.location.latitude,status:t,remark:this.clockRemark,filePath:this.filStr};console.log("打卡数据:",e);const s=await r(e);200===s.code?(this.isClockIn?(this.clockInTime=this.currentTime,this.clockInStatus="in",i.length>0&&(this.signFilePath=i),this.compareTime(this.currentTime,this.workStartTime)>0?this.$toast("您已迟到，签到成功但请注意考勤"):this.$toast.success("签到成功")):(this.clockOutTime=this.currentTime,this.clockInStatus="out",i.length>0&&(this.outFilePath=i),this.compareTime(this.currentTime,this.workEndTime)<0?this.$toast("您已早退，签退成功但请注意考勤"):this.$toast.success("签退成功")),setTimeout(()=>{this.getCurrentClockInfo()},1e3)):this.$toast.fail(s.msg||"打卡失败")}catch(i){console.error("打卡异常:",i),this.$toast.fail("打卡失败，请稍后重试")}finally{this.loading=!1,this.$toast.clear(),this.showPopup=!1,this.clockRemark="",this.filStr=""}},compareTime(t,i){const e=t=>{const[i,e,s]=t.split(":").map(Number);return 3600*i+60*e+(s||0)};return e(t)-e(i)},previewImage(t,i){t&&0!==t.length&&this.$imagePreview({images:t,startPosition:i,closeable:!0,closeIconPosition:"top-right"})},onSignSwipeChange(t){this.signActiveIndex=t},onOutSwipeChange(t){this.outActiveIndex=t},handleReport(){this.location.longitude&&this.location.latitude?this.$dialog.confirm({title:"确认上报",message:"确定要上报当前位置吗？",confirmButtonText:"确认上报",cancelButtonText:"取消"}).then(async()=>{try{this.$toast.loading({message:"上报中...",forbidClick:!0});const t={nowLongitude:this.location.longitude,nowLatitude:this.location.latitude,status:3};console.log("上报数据:",t);const i=await r(t);200===i.code?(this.$toast.success("上报成功"),setTimeout(()=>{this.getCurrentClockInfo()},1e3)):this.$toast.fail(i.msg||"上报失败")}catch(t){console.error("上报异常:",t),this.$toast.fail("上报失败，请稍后重试")}finally{this.$toast.clear()}}).catch(()=>{}):this.$dialog.confirm({title:"位置信息未获取",message:"需要获取您的位置信息才能上报，是否重新获取？",confirmButtonText:"重新获取",cancelButtonText:"取消"}).then(()=>{this.getLocation()}).catch(()=>{})}}},p=d,m=(e("f513"),e("2877")),g=Object(m["a"])(p,s,a,!1,null,"9e4b3418",null);i["default"]=g.exports},"58d9":function(t,i,e){"use strict";e("629c")},"629c":function(t,i,e){},"837c":function(t,i){t.exports="data:image/png;base64,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"},"847b":function(t,i,e){t.exports=e.p+"static/img/avatar.bb9f89ad.png"},f513:function(t,i,e){"use strict";e("259f")}}]);