// 讯飞语音识别配置
export const speechConfig = {
  // 讯飞开放平台应用配置
  appId: process.env.VUE_APP_XUNFEI_APP_ID || 'c25b3295',
  apiKey: process.env.VUE_APP_XUNFEI_API_KEY || 'f0116c2b873eaa51a1c25bab8cad486d',
  apiSecret: process.env.VUE_APP_XUNFEI_API_SECRET || 'ZTY1NzkyZjRkZjZkYjE2NzA1ODdiOWVj',

  // WebSocket地址
  hostUrl: 'wss://iat-api.xfyun.cn/v2/iat',

  // 音频参数
  audioConfig: {
    sampleRate: 16000,        // 采样率
    channelCount: 1,          // 声道数
    echoCancellation: true,   // 回声消除
    noiseSuppression: true,   // 噪声抑制
    frameSize: 1280          // 每次发送的音频帧大小
  },

  // 识别参数
  recognitionConfig: {
    language: 'zh_cn',        // 语言
    accent: 'mandarin',       // 方言
    domain: 'iat',           // 领域
    ptt: 0,                  // 标点符号
    pd: 'edu',               // 垂直领域个性化参数
    rlang: 'zh-cn',          // 返回的语言
    vinfo: 1,                // 是否返回置信度
    vad_eos: 10000,          // 语音断尾检测
    nunum: 0                 // 将返回结果的数字格式规范化
  }
}

// 获取完整的语音识别配置
export function getSpeechConfig() {
  const config = {
    ...speechConfig,
    // 可以在这里添加运行时的配置覆盖
  }

  // 调试信息
  console.log('获取语音识别配置:', {
    appId: config.appId,
    hasApiKey: !!config.apiKey,
    hasApiSecret: !!config.apiSecret,
    hostUrl: config.hostUrl,
    envAppId: process.env.VUE_APP_XUNFEI_APP_ID,
    envHasApiKey: !!process.env.VUE_APP_XUNFEI_API_KEY,
    envHasApiSecret: !!process.env.VUE_APP_XUNFEI_API_SECRET
  });

  return config;
}

// 验证配置是否完整
export function validateSpeechConfig(config = speechConfig) {
  const requiredFields = ['appId', 'apiKey', 'apiSecret'];
  const missingFields = requiredFields.filter(field =>
    !config[field] || config[field] === 'your_app_id' ||
    config[field] === 'your_api_key' || config[field] === 'your_api_secret'
  );

  if (missingFields.length > 0) {
    console.warn('讯飞语音识别配置不完整，缺少字段:', missingFields);
    return false;
  }

  return true;
}
