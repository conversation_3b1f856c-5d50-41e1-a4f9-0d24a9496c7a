(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-619be9a5"],{"35a5":function(t,e,s){"use strict";s("c0bc")},"819b":function(t,e,s){},b8a2:function(t,e,s){"use strict";s("c122")},c0bc:function(t,e,s){},c122:function(t,e,s){},e04c:function(t,e,s){"use strict";s.r(e);var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"work-stats"},[e("van-nav-bar",{attrs:{title:"工作统计","left-arrow":""},on:{"click-left":t.onClickLeft}}),e("div",{staticClass:"header"},[e("div",{staticClass:"date-picker-wrapper"},[e("van-field",{attrs:{readonly:"",label:"日期范围",placeholder:"选择日期范围"},on:{click:function(e){t.showCalendar=!0}},model:{value:t.dateRange,callback:function(e){t.dateRange=e},expression:"dateRange"}}),e("van-button",{staticStyle:{width:"80px","margin-right":"8px"},attrs:{type:"info",size:"small"},on:{click:t.search}},[t._v("搜索")])],1)]),t.isShow?t._e():e("div",{staticClass:"stats-list"},[e("div",{staticClass:"stats-item"},[e("van-cell",{staticClass:"stats-cell",attrs:{value:t.stats.czCount||0,"value-class":"stats-value"},scopedSlots:t._u([{key:"icon",fn:function(){return[e("van-icon",{staticClass:"stats-icon",attrs:{name:"records"}})]},proxy:!0},{key:"title",fn:function(){return[e("span",{staticClass:"stats-title"},[t._v("处置数")])]},proxy:!0},{key:"value",fn:function(){return[e("span",{staticClass:"stats-value"},[t._v(t._s(t.stats.czCount))])]},proxy:!0}],null,!1,3863306156)})],1),e("div",{staticClass:"stats-item"},[e("van-cell",{staticClass:"stats-cell",attrs:{value:t.stats.asczCount||0,"value-class":"stats-value"},scopedSlots:t._u([{key:"icon",fn:function(){return[e("van-icon",{staticClass:"stats-icon",attrs:{name:"records"}})]},proxy:!0},{key:"title",fn:function(){return[e("span",{staticClass:"stats-title"},[t._v("按时处置数")])]},proxy:!0},{key:"value",fn:function(){return[e("span",{staticClass:"stats-value"},[t._v(t._s(t.stats.asczCount))])]},proxy:!0}],null,!1,2158947969)})],1),e("div",{staticClass:"stats-item"},[e("van-cell",{staticClass:"stats-cell",attrs:{value:t.stats.cqczCount||0,"value-class":"stats-value"},scopedSlots:t._u([{key:"icon",fn:function(){return[e("van-icon",{staticClass:"stats-icon",attrs:{name:"records"}})]},proxy:!0},{key:"title",fn:function(){return[e("span",{staticClass:"stats-title"},[t._v("超期处置数")])]},proxy:!0},{key:"value",fn:function(){return[e("span",{staticClass:"stats-value"},[t._v(t._s(t.stats.cqczCount))])]},proxy:!0}],null,!1,2516410020)})],1),e("div",{staticClass:"stats-item"},[e("van-cell",{staticClass:"stats-cell",attrs:{value:t.stats.backCount||0,"value-class":"stats-value"},scopedSlots:t._u([{key:"icon",fn:function(){return[e("van-icon",{staticClass:"stats-icon",attrs:{name:"records"}})]},proxy:!0},{key:"title",fn:function(){return[e("span",{staticClass:"stats-title"},[t._v("返工数")])]},proxy:!0},{key:"value",fn:function(){return[e("span",{staticClass:"stats-value"},[t._v(t._s(t.stats.backCount))])]},proxy:!0}],null,!1,220432293)})],1)]),t.isShow?e("div",{staticClass:"stats-list"},[e("div",{staticClass:"stats-card"},[e("div",{staticClass:"card-header"},[t._v("上报统计数")]),e("div",{staticClass:"card-content"},[e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-value"},[t._v(t._s(t.stats.reportCount||0))]),e("div",{staticClass:"stat-label"},[t._v("上报数")])]),e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-value"},[t._v(t._s(t.stats.effectCount||0))]),e("div",{staticClass:"stat-label"},[t._v("有效上报数")])]),e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-value"},[t._v(t._s(t.stats.noEffectCount||0))]),e("div",{staticClass:"stat-label"},[t._v("无效上报数")])])])]),e("div",{staticClass:"stats-card"},[e("div",{staticClass:"card-header"},[t._v("核实统计数")]),e("div",{staticClass:"card-content"},[e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-value"},[t._v(t._s(t.stats.shouldHesCount||0))]),e("div",{staticClass:"stat-label"},[t._v("应核实数")])]),e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-value"},[t._v(t._s(t.stats.HesCount||0))]),e("div",{staticClass:"stat-label"},[t._v("核实数")])]),e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-value"},[t._v(t._s(t.stats.OnHesCount||0))]),e("div",{staticClass:"stat-label"},[t._v("按时核实数")])])])]),e("div",{staticClass:"stats-card"},[e("div",{staticClass:"card-header"},[t._v("核查统计数")]),e("div",{staticClass:"card-content"},[e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-value"},[t._v(t._s(t.stats.shouldHecCount||0))]),e("div",{staticClass:"stat-label"},[t._v("应核查数")])]),e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-value"},[t._v(t._s(t.stats.HecCount||0))]),e("div",{staticClass:"stat-label"},[t._v("核查数")])]),e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-value"},[t._v(t._s(t.stats.OnHecCount||0))]),e("div",{staticClass:"stat-label"},[t._v("按时核查数")])])])])]):t._e(),e("van-calendar",{attrs:{type:"range","min-date":t.minDate},on:{confirm:t.onDateConfirm,close:function(e){t.showCalendar=!1}},model:{value:t.showCalendar,callback:function(e){t.showCalendar=e},expression:"showCalendar"}})],1)},i=[],n=(s("c3a6"),s("ad06")),r=(s("68ef"),s("a71a"),s("9d70"),s("3743"),s("4d75"),s("e3b3"),s("bc1b"),s("b258"),s("819b"),s("14d9"),s("13d5"),s("e9f5"),s("7d54"),s("ab43"),s("9485"),s("a732"),s("4598")),o=s("bad1"),l=s("a8c1"),c=s("d282"),u=Object(c["a"])("calendar"),h=u[0],d=u[1],f=u[2];function v(t){return f("monthTitle",t.getFullYear(),t.getMonth()+1)}function m(t,e){var s=t.getFullYear(),a=e.getFullYear(),i=t.getMonth(),n=e.getMonth();return s===a?i===n?0:i>n?1:-1:s>a?1:-1}function g(t,e){var s=m(t,e);if(0===s){var a=t.getDate(),i=e.getDate();return a===i?0:a>i?1:-1}return s}function p(t,e){return t=new Date(t),t.setDate(t.getDate()+e),t}function y(t){return p(t,-1)}function C(t){return p(t,1)}function D(t){var e=t[0].getTime(),s=t[1].getTime();return(s-e)/864e5+1}function b(t){return new Date(t)}function w(t){return Array.isArray(t)?t.map((function(t){return null===t?t:b(t)})):b(t)}var k=s("e41f"),S=s("b650"),_=s("d399"),x=s("ea8e"),O=s("96b0"),T=Object(c["a"])("calendar-month"),$=T[0],B=$({props:{date:Date,type:String,color:String,minDate:Date,maxDate:Date,showMark:Boolean,rowHeight:[Number,String],formatter:Function,lazyRender:Boolean,currentDate:[Date,Array],allowSameDay:Boolean,showSubtitle:Boolean,showMonthTitle:Boolean,firstDayOfWeek:Number},data:function(){return{visible:!1}},computed:{title:function(){return v(this.date)},rowHeightWithUnit:function(){return Object(x["a"])(this.rowHeight)},offset:function(){var t=this.firstDayOfWeek,e=this.date.getDay();return t?(e+7-this.firstDayOfWeek)%7:e},totalDay:function(){return Object(O["a"])(this.date.getFullYear(),this.date.getMonth()+1)},shouldRender:function(){return this.visible||!this.lazyRender},placeholders:function(){for(var t=[],e=Math.ceil((this.totalDay+this.offset)/7),s=1;s<=e;s++)t.push({type:"placeholder"});return t},days:function(){for(var t=[],e=this.date.getFullYear(),s=this.date.getMonth(),a=1;a<=this.totalDay;a++){var i=new Date(e,s,a),n=this.getDayType(i),r={date:i,type:n,text:a,bottomInfo:this.getBottomInfo(n)};this.formatter&&(r=this.formatter(r)),t.push(r)}return t}},methods:{getHeight:function(){return this.height||(this.height=this.$el.getBoundingClientRect().height),this.height},scrollIntoView:function(t){var e=this.$refs,s=e.days,a=e.month,i=this.showSubtitle?s:a,n=i.getBoundingClientRect().top-t.getBoundingClientRect().top+t.scrollTop;Object(l["h"])(t,n)},getMultipleDayType:function(t){var e=this,s=function(t){return e.currentDate.some((function(e){return 0===g(e,t)}))};if(s(t)){var a=y(t),i=C(t),n=s(a),r=s(i);return n&&r?"multiple-middle":n?"end":r?"start":"multiple-selected"}return""},getRangeDayType:function(t){var e=this.currentDate,s=e[0],a=e[1];if(!s)return"";var i=g(t,s);if(!a)return 0===i?"start":"";var n=g(t,a);return 0===i&&0===n&&this.allowSameDay?"start-end":0===i?"start":0===n?"end":i>0&&n<0?"middle":void 0},getDayType:function(t){var e=this.type,s=this.minDate,a=this.maxDate,i=this.currentDate;return g(t,s)<0||g(t,a)>0?"disabled":null!==i?"single"===e?0===g(t,i)?"selected":"":"multiple"===e?this.getMultipleDayType(t):"range"===e?this.getRangeDayType(t):void 0:void 0},getBottomInfo:function(t){if("range"===this.type){if("start"===t||"end"===t)return f(t);if("start-end"===t)return f("startEnd")}},getDayStyle:function(t,e){var s={height:this.rowHeightWithUnit};return"placeholder"===t?(s.width="100%",s):(0===e&&(s.marginLeft=100*this.offset/7+"%"),this.color&&("start"===t||"end"===t||"start-end"===t||"multiple-selected"===t||"multiple-middle"===t?s.background=this.color:"middle"===t&&(s.color=this.color)),s)},genTitle:function(){var t=this.$createElement;if(this.showMonthTitle)return t("div",{class:d("month-title")},[this.title])},genMark:function(){var t=this.$createElement;if(this.showMark&&this.shouldRender)return t("div",{class:d("month-mark")},[this.date.getMonth()+1])},genDays:function(){var t=this.$createElement,e=this.shouldRender?this.days:this.placeholders;return t("div",{ref:"days",attrs:{role:"grid"},class:d("days")},[this.genMark(),e.map(this.genDay)])},genDay:function(t,e){var s=this,a=this.$createElement,i=t.type,n=t.topInfo,r=t.bottomInfo,o=this.getDayStyle(i,e),l="disabled"===i,c=function(){l||s.$emit("click",t)},u=n&&a("div",{class:d("top-info")},[n]),h=r&&a("div",{class:d("bottom-info")},[r]);return"selected"===i?a("div",{attrs:{role:"gridcell",tabindex:-1},style:o,class:[d("day"),t.className],on:{click:c}},[a("div",{class:d("selected-day"),style:{width:this.rowHeightWithUnit,height:this.rowHeightWithUnit,background:this.color}},[u,t.text,h])]):a("div",{attrs:{role:"gridcell",tabindex:l?null:-1},style:o,class:[d("day",i),t.className],on:{click:c}},[u,t.text,h])}},render:function(){var t=arguments[0];return t("div",{class:d("month"),ref:"month"},[this.genTitle(),this.genDays()])}}),R=Object(c["a"])("calendar-header"),M=R[0],H=M({props:{title:String,subtitle:String,showTitle:Boolean,showSubtitle:Boolean,firstDayOfWeek:Number},methods:{genTitle:function(){var t=this.$createElement;if(this.showTitle){var e=this.slots("title")||this.title||f("title");return t("div",{class:d("header-title")},[e])}},genSubtitle:function(){var t=this.$createElement;if(this.showSubtitle)return t("div",{class:d("header-subtitle")},[this.subtitle])},genWeekDays:function(){var t=this.$createElement,e=f("weekdays"),s=this.firstDayOfWeek,a=[].concat(e.slice(s,7),e.slice(0,s));return t("div",{class:d("weekdays")},[a.map((function(e){return t("span",{class:d("weekday")},[e])}))])}},render:function(){var t=arguments[0];return t("div",{class:d("header")},[this.genTitle(),this.genSubtitle(),this.genWeekDays()])}}),W=h({props:{title:String,color:String,value:Boolean,readonly:Boolean,formatter:Function,rowHeight:[Number,String],confirmText:String,rangePrompt:String,defaultDate:[Date,Array],getContainer:[String,Function],allowSameDay:Boolean,confirmDisabledText:String,type:{type:String,default:"single"},round:{type:Boolean,default:!0},position:{type:String,default:"bottom"},poppable:{type:Boolean,default:!0},maxRange:{type:[Number,String],default:null},lazyRender:{type:Boolean,default:!0},showMark:{type:Boolean,default:!0},showTitle:{type:Boolean,default:!0},showConfirm:{type:Boolean,default:!0},showSubtitle:{type:Boolean,default:!0},closeOnPopstate:{type:Boolean,default:!0},closeOnClickOverlay:{type:Boolean,default:!0},safeAreaInsetBottom:{type:Boolean,default:!0},minDate:{type:Date,validator:o["a"],default:function(){return new Date}},maxDate:{type:Date,validator:o["a"],default:function(){var t=new Date;return new Date(t.getFullYear(),t.getMonth()+6,t.getDate())}},firstDayOfWeek:{type:[Number,String],default:0,validator:function(t){return t>=0&&t<=6}}},data:function(){return{subtitle:"",currentDate:this.getInitialDate()}},computed:{months:function(){var t=[],e=new Date(this.minDate);e.setDate(1);do{t.push(new Date(e)),e.setMonth(e.getMonth()+1)}while(1!==m(e,this.maxDate));return t},buttonDisabled:function(){var t=this.type,e=this.currentDate;if(e){if("range"===t)return!e[0]||!e[1];if("multiple"===t)return!e.length}return!e},dayOffset:function(){return this.firstDayOfWeek?this.firstDayOfWeek%7:0}},watch:{value:"init",type:function(){this.reset()},defaultDate:function(t){this.currentDate=t,this.scrollIntoView()}},mounted:function(){this.init()},activated:function(){this.init()},methods:{reset:function(t){void 0===t&&(t=this.getInitialDate()),this.currentDate=t,this.scrollIntoView()},init:function(){var t=this;this.poppable&&!this.value||this.$nextTick((function(){t.bodyHeight=Math.floor(t.$refs.body.getBoundingClientRect().height),t.onScroll(),t.scrollIntoView()}))},scrollToDate:function(t){var e=this;Object(r["c"])((function(){var s=e.value||!e.poppable;t&&s&&(e.months.some((function(s,a){if(0===m(s,t)){var i=e.$refs,n=i.body,r=i.months;return r[a].scrollIntoView(n),!0}return!1})),e.onScroll())}))},scrollIntoView:function(){var t=this.currentDate;if(t){var e="single"===this.type?t:t[0];this.scrollToDate(e)}},getInitialDate:function(){var t=this.type,e=this.minDate,s=this.maxDate,a=this.defaultDate;if(null===a)return a;var i=new Date;if(-1===g(i,e)?i=e:1===g(i,s)&&(i=s),"range"===t){var n=a||[],r=n[0],o=n[1];return[r||i,o||C(i)]}return"multiple"===t?a||[i]:a||i},onScroll:function(){var t=this.$refs,e=t.body,s=t.months,a=Object(l["c"])(e),i=a+this.bodyHeight,n=s.map((function(t){return t.getHeight()})),r=n.reduce((function(t,e){return t+e}),0);if(!(i>r&&a>0)){for(var o,c=0,u=[-1,-1],h=0;h<s.length;h++){var d=c<=i&&c+n[h]>=a;d&&(u[1]=h,o||(o=s[h],u[0]=h),s[h].showed||(s[h].showed=!0,this.$emit("month-show",{date:s[h].date,title:s[h].title}))),c+=n[h]}s.forEach((function(t,e){t.visible=e>=u[0]-1&&e<=u[1]+1})),o&&(this.subtitle=o.title)}},onClickDay:function(t){if(!this.readonly){var e=t.date,s=this.type,a=this.currentDate;if("range"===s){if(!a)return void this.select([e,null]);var i=a[0],n=a[1];if(i&&!n){var r=g(e,i);1===r?this.select([i,e],!0):-1===r?this.select([e,null]):this.allowSameDay&&this.select([e,e],!0)}else this.select([e,null])}else if("multiple"===s){if(!a)return void this.select([e]);var o,l=this.currentDate.some((function(t,s){var a=0===g(t,e);return a&&(o=s),a}));if(l){var c=a.splice(o,1),u=c[0];this.$emit("unselect",b(u))}else this.maxRange&&a.length>=this.maxRange?Object(_["a"])(this.rangePrompt||f("rangePrompt",this.maxRange)):this.select([].concat(a,[e]))}else this.select(e,!0)}},togglePopup:function(t){this.$emit("input",t)},select:function(t,e){var s=this,a=function(t){s.currentDate=t,s.$emit("select",w(s.currentDate))};if(e&&"range"===this.type){var i=this.checkRange(t);if(!i)return void(this.showConfirm?a([t[0],p(t[0],this.maxRange-1)]):a(t))}a(t),e&&!this.showConfirm&&this.onConfirm()},checkRange:function(t){var e=this.maxRange,s=this.rangePrompt;return!(e&&D(t)>e)||(Object(_["a"])(s||f("rangePrompt",e)),!1)},onConfirm:function(){this.$emit("confirm",w(this.currentDate))},genMonth:function(t,e){var s=this.$createElement,a=0!==e||!this.showSubtitle;return s(B,{ref:"months",refInFor:!0,attrs:{date:t,type:this.type,color:this.color,minDate:this.minDate,maxDate:this.maxDate,showMark:this.showMark,formatter:this.formatter,rowHeight:this.rowHeight,lazyRender:this.lazyRender,currentDate:this.currentDate,showSubtitle:this.showSubtitle,allowSameDay:this.allowSameDay,showMonthTitle:a,firstDayOfWeek:this.dayOffset},on:{click:this.onClickDay}})},genFooterContent:function(){var t=this.$createElement,e=this.slots("footer");if(e)return e;if(this.showConfirm){var s=this.buttonDisabled?this.confirmDisabledText:this.confirmText;return t(S["a"],{attrs:{round:!0,block:!0,type:"danger",color:this.color,disabled:this.buttonDisabled,nativeType:"button"},class:d("confirm"),on:{click:this.onConfirm}},[s||f("confirm")])}},genFooter:function(){var t=this.$createElement;return t("div",{class:d("footer",{unfit:!this.safeAreaInsetBottom})},[this.genFooterContent()])},genCalendar:function(){var t=this,e=this.$createElement;return e("div",{class:d()},[e(H,{attrs:{title:this.title,showTitle:this.showTitle,subtitle:this.subtitle,showSubtitle:this.showSubtitle,firstDayOfWeek:this.dayOffset},scopedSlots:{title:function(){return t.slots("title")}}}),e("div",{ref:"body",class:d("body"),on:{scroll:this.onScroll}},[this.months.map(this.genMonth)]),this.genFooter()])}},render:function(){var t=this,e=arguments[0];if(this.poppable){var s,a=function(e){return function(){return t.$emit(e)}};return e(k["a"],{attrs:(s={round:!0,value:this.value},s["round"]=this.round,s["position"]=this.position,s["closeable"]=this.showTitle||this.showSubtitle,s["getContainer"]=this.getContainer,s["closeOnPopstate"]=this.closeOnPopstate,s["closeOnClickOverlay"]=this.closeOnClickOverlay,s),class:d("popup"),on:{input:this.togglePopup,open:a("open"),opened:a("opened"),close:a("close"),closed:a("closed")}},[this.genCalendar()])}return this.genCalendar()}});function z(t){const e=new Date(t),s=e.getFullYear(),a=String(e.getMonth()+1).padStart(2,"0"),i=String(e.getDate()).padStart(2,"0");return`${s}-${a}-${i}`}var I=s("4020");const E=t=>I["a"].get("/zhcg/event/getYdWorkStatistics",{params:t}),F=t=>I["a"].get("/zhcg/event/getZybmWorkStatistics",{params:t});var j={name:"WorkStats",components:{[W.name]:W,[n["a"].name]:n["a"]},data(){return{dateRange:"",showCalendar:!1,minDate:new Date(2020,0,1),stats:{czCount:0,asczCount:0,cqczCount:0,backCount:0,reportCount:0,effectCount:0,noEffectCount:0,shouldHesCount:0,HesCount:0,OnHesCount:0,shouldHecCount:0,HecCount:0,OnHecCount:0}}},created(){const t=new Date,e=new Date;e.setDate(e.getDate()-7),this.startDate=z(e),this.endDate=z(t),this.search()},computed:{isShow(){return"1"===this.$store.state.user.roleType}},methods:{onClickLeft(){this.$router.back()},onDateConfirm([t,e]){this.dateRange=`${z(t)} 至 ${z(e)}`,this.startDate=z(t),this.endDate=z(e),this.showCalendar=!1,this.search()},async search(){try{let t={beginTime:this.startDate,endTime:this.endDate};this.startDate&&this.endDate?(t.beginTime=this.startDate,t.endTime=this.endDate):t=null;const e=this.isShow?await E({params:t}):await F({params:t});this.stats=e.data}catch(t){console.log(t),this.$toast.fail("获取数据失败")}}}},P=j,N=(s("b8a2"),s("35a5"),s("2877")),Y=Object(N["a"])(P,a,i,!1,null,"57d8b3d4",null);e["default"]=Y.exports}}]);