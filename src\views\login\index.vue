<template>
  <div class="login-container">
    <!-- 顶部图片区域 -->
    <div class="login-banner">
      <img src="@/assets/login/loginBg.png" alt="登录banner">
    </div>

    <!-- 登录表单区域 -->
    <div class="login-form">
      <van-form @submit="onSubmit">
        <van-field
          v-model="loginForm.username"
          placeholder="请输入账号"
          :rules="[{ required: true, message: '请输入账号' }]"
        >
          <template #left-icon>
            <van-icon name="user-o" />
          </template>
        </van-field>

        <van-field
          v-model="loginForm.password"
          type="password"
          placeholder="请输入密码"
          :rules="[{ required: true, message: '请输入密码' }]"
        >
          <template #left-icon>
            <van-icon name="lock" />
          </template>
        </van-field>

        <van-field
          v-model="loginForm.code"
          placeholder="请输入验证码"
          :rules="[{ required: true, message: '请输入验证码' }]"
        >
          <template #left-icon>
            <van-icon name="shield-o" />
          </template>
          <template #right-icon>
            <img
              :src="codeUrl"
              class="captcha-img"
              @click="getCode"
              alt="验证码"
            >
          </template>
        </van-field>

        <div class="form-btn">
          <van-button round block type="info" native-type="submit">
            登录
          </van-button>
        </div>
      </van-form>
    </div>
  </div>
</template>

<script>
import { login, getCodeImg, getInfo } from '@/api/login'
import { setToken } from "@/util/auth";

export default {
  name: "Login",
  data() {
    return {
      loginForm: {
        username: '',
        password: '',
        code: '',
        uuid: ''
      },
      codeUrl: ''
    }
  },
  created() {
    this.getCode()
  },
  methods: {
    async getCode() {
      const res = await getCodeImg()
      this.codeUrl = 'data:image/gif;base64,' + res.img
      this.loginForm.uuid = res.uuid
    },
    async onSubmit() {
      login(this.loginForm).then((res) => {
        setToken(res.token)
        this.GetInfo(res.token)
        this.$toast.success('登录成功')
        this.$router.push('/')
      }).catch((error) => {
        this.$toast.fail(error.msg)
        // 登录失败时刷新验证码
        this.getCode()
      })
    },
    GetInfo(token) {
      getInfo(token)
          .then((res) => {
            const user = res.user
            const userObj = {
              deptName: user?.dept?.deptName || '',
              deptId: user?.dept?.deptId || '',
              nickName: user.nickName,
              ...user,
              id: res?.dogUserQualifi?.id || '',
              realName: user.userName || '',
              mobile: user.phonenumber || '',
              userQualifi: res?.dogUserQualifi || undefined,
              qualifi: res?.dogQualifi || undefined,
              roleType: res.roleType
            }
            this.vuex("user", userObj);
          })
    }
  }
}
</script>

<style scoped lang="scss">
.login-container {
  min-height: 100vh;
  background: #fff;
}

.login-banner {
  position: relative;
  text-align: center;
  padding: 8vh 0 4vh;
}

.login-banner img {
  width: 100%;
  max-width: 90vw;
  max-height: 30vh;
  object-fit: contain;
}

.login-form {
  padding: 2vh 2.5vw;
  max-width: 90%;
  margin: 0 auto;
}

.form-btn {
  margin-top: 3vh;
}

:deep(.van-field__left-icon) {
  margin-right: 1.5vw;
  display: flex;
  justify-content: center;
  align-items: center;
  .van-icon {
    font-size: 15px;
  }
}

:deep(.van-field__error-message) {
  font-size: 0;
  height: 0;
}

.captcha-img {
  height: 36px;
  cursor: pointer;
}

:deep(.van-field) {
  margin-bottom: 1vh;
  font-size: 15px;
}

:deep(.van-button) {
  height: 50px;
  font-size: 16.5px;
}
</style>
