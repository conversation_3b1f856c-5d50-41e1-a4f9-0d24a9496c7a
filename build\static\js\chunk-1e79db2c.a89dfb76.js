(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1e79db2c"],{"20e4":function(t,a,e){"use strict";e("a29e")},"3d8a":function(t,a,e){"use strict";e.r(a);var i=function(){var t=this,a=t._self._c;return a("div",{staticClass:"task-detail-page"},[a("van-nav-bar",{attrs:{title:"待办任务","left-arrow":"",fixed:""},on:{"click-left":t.onClickLeft}}),a("div",{staticClass:"tab-fixed"},[a("tabSwitch",{attrs:{tabs:t.tabOptions},on:{change:t.handleTabChange},model:{value:t.activeTab,callback:function(a){t.activeTab=a},expression:"activeTab"}})],1),t.taskDetail?a("div",{staticClass:"detail-content"},["基本信息"===t.activeTab?a("basic-information",{attrs:{"task-detail":t.taskDetail,opinion:t.query.opinion,"contact-phone":t.query.contactPhone,"file-str":t.query.fileStr,coordinates:t.coordinates},on:{"update:opinion":function(a){return t.$set(t.query,"opinion",a)},"update:contactPhone":function(a){return t.$set(t.query,"contactPhone",a)},"update:contact-phone":function(a){return t.$set(t.query,"contactPhone",a)},"update:fileStr":function(a){return t.$set(t.query,"fileStr",a)},"update:file-str":function(a){return t.$set(t.query,"fileStr",a)},upload:t.afterRead}}):t._e(),"流程跟踪"===t.activeTab?a("flow-tracing"):t._e()],1):t._e(),a("div",{staticClass:"bottom-btns"},[a("van-button",{staticClass:"cancel-btn",attrs:{plain:""},on:{click:t.onCancel}},[t._v("取消")]),a("van-button",{staticClass:"submit-btn",attrs:{type:"info"},on:{click:t.onSubmit}},[t._v("反馈处置结果")]),a("van-button",{staticClass:"submit-btn",attrs:{type:"info"},on:{click:t.ApplyForReturn}},[t._v("申请退回")]),a("van-button",{staticClass:"submit-btn",attrs:{type:"info"},on:{click:t.ApplyForExtension}},[t._v("申请延期")])],1)],1)},s=[],o=(e("14d9"),e("e9f5"),e("ab43"),e("eeb8")),n=function(){var t=this,a=t._self._c;return a("div",{staticClass:"basic-info"},[a("div",{staticClass:"info-item special"},[a("div",{staticClass:"item-label"},[t.taskDetail.urgent&&1==t.taskDetail.urgent?a("van-tag",{staticClass:"dangerBtn",attrs:{type:"danger",plain:""}},[t._v("紧急")]):t._e(),a("span",{staticClass:"num-label"},[t._v(t._s(t.taskDetail.id||"暂无数据"))])],1)]),a("div",{staticClass:"info-list"},[a("div",{staticClass:"info-item"},[a("div",{staticClass:"item-label"},[t._v("问题来源")]),a("div",{staticClass:"item-value"},[t._v(t._s(t.taskDetail.source||"暂无数据"))])]),a("div",{staticClass:"info-item"},[a("div",{staticClass:"item-label"},[t._v("问题类型")]),a("div",{staticClass:"item-value"},[t._v(t._s(t.taskDetail.type||"暂无数据"))])]),a("div",{staticClass:"info-item"},[a("div",{staticClass:"item-label"},[t._v("大类名称")]),a("div",{staticClass:"item-value"},[t._v(t._s(t.taskDetail.mainCategory||"暂无数据"))])]),a("div",{staticClass:"info-item"},[a("div",{staticClass:"item-label"},[t._v("小类名称")]),a("div",{staticClass:"item-value"},[t._v(t._s(t.taskDetail.subCategory||"暂无数据"))])]),a("div",{staticClass:"info-item"},[a("div",{staticClass:"item-label"},[t._v("立案标准")]),a("div",{staticClass:"item-value"},[t._v(t._s(t.taskDetail.labz||"暂无数据"))])]),a("div",{staticClass:"info-item"},[a("div",{staticClass:"item-label"},[t._v("所属区县")]),a("div",{staticClass:"item-value"},[t._v(t._s(t.taskDetail.community||"暂无数据"))])]),a("div",{staticClass:"info-item"},[a("div",{staticClass:"item-label"},[t._v("所属街道")]),a("div",{staticClass:"item-value"},[t._v(t._s(t.taskDetail.street||"暂无数据"))])]),a("div",{staticClass:"info-item"},[a("div",{staticClass:"item-label"},[t._v("事发地址")]),a("div",{staticClass:"item-value"},[t._v(t._s(t.taskDetail.address||"暂无数据"))])]),a("div",{staticClass:"info-item"},[a("div",{staticClass:"item-label"},[t._v("问题描述")]),a("div",{staticClass:"item-value"},[t._v(t._s(t.taskDetail.description||"暂无数据"))])])]),a("div",{staticClass:"map-location"},[a("div",{staticClass:"map-container",on:{click:function(a){t.showMapDialog=!0}}},[a("img",{staticClass:"map-image",attrs:{src:e("a5d1"),alt:"地图位置"}}),a("div",{staticClass:"map-marker"},[a("van-icon",{attrs:{name:"location-o",size:"24",color:"#1989fa"}}),a("div",{staticClass:"marker-label"},[t._v("点击查看点位")])],1)])]),a("div",{staticClass:"attachment-area"},[a("div",{staticClass:"attachment-label"},[t._v("现场图片")]),a("div",{staticClass:"upload-placeholder"},[a("vantFileUpload",{attrs:{"file-type":["jpg","png"]},on:{change:t.updateFileStr},model:{value:t.fileStrLocal,callback:function(a){t.fileStrLocal=a},expression:"fileStrLocal"}})],1)]),a("div",{staticClass:"remark-area"},[a("div",{staticClass:"remark-label-container"},[a("div",{staticClass:"remark-label"},[t._v("处置结果")]),a("idioms-selector",{on:{select:t.selectIdiom}},[t._v("选择惯用语")])],1),a("van-field",{attrs:{type:"textarea",placeholder:"请输入说明内容",rows:"1",autosize:""},on:{input:t.updateOpinion},model:{value:t.opinionLocal,callback:function(a){t.opinionLocal=a},expression:"opinionLocal"}})],1),a("div",{staticClass:"remark-area"},[t._m(0),a("van-field",{attrs:{type:"textarea",placeholder:"请输入联系电话",rows:"1",autosize:""},on:{input:t.updatecontactPhone},model:{value:t.contactPhoneLocal,callback:function(a){t.contactPhoneLocal=a},expression:"contactPhoneLocal"}})],1),a("van-popup",{style:{height:"80%",width:"100%"},attrs:{position:"bottom"},model:{value:t.showMapDialog,callback:function(a){t.showMapDialog=a},expression:"showMapDialog"}},[a("div",{staticClass:"map-dialog"},[t.showMapDialog?a("Map",{ref:"mapComponent",attrs:{coordinates:t.coordinates},on:{locationSelected:t.handleLocationSelected}}):t._e(),a("div",{staticClass:"map-dialog-footer"},[a("van-button",{attrs:{type:"primary",block:""},on:{click:t.confirmLocation}},[t._v("确认位置")])],1)],1)])],1)},l=[function(){var t=this,a=t._self._c;return a("div",{staticClass:"remark-label-container"},[a("div",{staticClass:"remark-label"},[t._v("联系电话")])])}],c=(e("f665"),e("6e29")),r=e("5945"),d=e("207e"),p={name:"BasicInformation",components:{IdiomsSelector:c["a"],Map:r["a"],vantFileUpload:d["a"]},props:{taskDetail:{type:Object,default:()=>({})},opinion:{type:String,default:""},contactPhone:{type:String,default:""},fileStr:{type:String,default:""},coordinates:{type:Array,default:()=>[]}},data(){return{showMapDialog:!1,opinionLocal:this.opinion,contactPhoneLocal:this.contactPhone,fileStrLocal:this.fileStr,areaOptions:[]}},mounted(){this.getDictList()},watch:{opinion(t){this.opinionLocal=t},contactPhone(t){this.contactPhoneLocal=t},fileStr(t){this.fileStrLocal=t}},methods:{getDictList(){this.getDicts("county").then(t=>{this.areaOptions=t.data.map(t=>({label:t.dictLabel,value:t.dictValue}))})},onUpload(t){this.$emit("upload",t)},updateOpinion(t){this.$emit("update:opinion",t)},updatecontactPhone(t){this.$emit("update:contactPhone",t)},updateFileStr(t){this.$emit("update:fileStr",t)},selectIdiom(t){this.opinionLocal=t,this.updateOpinion(t)},handleLocationSelected(t){this.taskDetail.street=t.addressComponent.road,this.taskDetail.community=this.areaOptions.find(a=>a.label==t.addressComponent.county).value?this.areaOptions.find(a=>a.label==t.addressComponent.county).value:"",this.taskDetail.address=t.formatted_address},confirmLocation(){this.showMapDialog=!1}}},u=p,v=(e("3ea8"),e("2877")),m=Object(v["a"])(u,n,l,!1,null,"28c2e744",null),h=m.exports,f=function(){var t=this,a=t._self._c;return a("div",{staticClass:"process-tracking"},[a("div",{staticClass:"timeline"},t._l(t.processHistory,(function(e,i){return a("div",{key:i,staticClass:"timeline-item"},[a("div",{staticClass:"timeline-left"},[a("div",{staticClass:"timeline-dot",class:{active:!0}}),i!==t.processHistory.length-1?a("div",{staticClass:"timeline-line"}):t._e()]),a("div",{staticClass:"timeline-content"},[a("div",{staticClass:"timeline-header"},[a("div",{staticClass:"timeline-header-left"},[a("span",{staticClass:"timeline-time"},[t._v(t._s(e.name))]),a("span",{staticClass:"timeline-time"},[t._v(t._s(e.time))])]),a("span",{staticClass:"timeline-location"},[t._v(t._s(e.departName))])]),t._l(e.list,(function(e,i){return a("div",{staticClass:"timeline-detail"},[a("p",[t._v("执行的操作："+t._s(e.content))]),a("p",[t._v("接收人："+t._s(e.person))]),a("p",[t._v("批转意见：")]),a("p",[t._v(t._s(e.opinion))])])}))],2)])})),0)])},b=[],C=(e("910d"),e("2934")),y={name:"FlowTracing",data(){return{processHistory:[]}},mounted(){this.getProcessHistory()},methods:{getProcessHistory(){Object(C["f"])({eventId:this.$route.query.id}).then(t=>{this.processHistory=t.data.map(t=>({name:this.getNodeType(t.type),time:t.time,departName:t.departName,list:t.list.map(t=>({content:t.operateName,person:t.operatePerson,opinion:t.opinion?t.opinion:null}))})).filter(t=>t.departName)})},getNodeType(t){const a={1:"上报",2:"立案",3:"派遣",4:"处置",5:"核查",6:"结案"};return a[t]||""}}},_=y,g=(e("ae7d"),Object(v["a"])(_,f,b,!1,null,"c065c83c",null)),k=g.exports,D={name:"ToDoTasksDetail",components:{tabSwitch:o["a"],BasicInformation:h,FlowTracing:k},data(){return{activeTab:"基本信息",tabOptions:[{label:"基本信息",value:"基本信息"},{label:"流程跟踪",value:"流程跟踪"}],taskDetail:null,isLoading:!1,query:{opinion:"",contactPhone:"",fileStr:""},coordinates:[],areaOptions:[],typeOptions:[],sourceOptions:[]}},mounted(){this.init()},methods:{async init(){await this.getDictsList(),await this.getTaskDetail()},async getDictsList(){this.getDicts("zhcg_wtlx").then(t=>{this.typeOptions=t.data.map(t=>({label:t.dictLabel,value:t.dictValue}))}),this.getDicts("zhcg_wtly").then(t=>{this.sourceOptions=t.data.map(t=>({label:t.dictLabel,value:t.dictValue}))}),this.getDicts("county").then(t=>{this.areaOptions=t.data.map(t=>({label:t.dictLabel,value:t.dictValue}))})},async getTaskDetail(){try{this.isLoading=!0,this.$toast.loading({message:"加载中...",forbidClick:!0,duration:0}),Object(C["k"])(this.$route.query.id).then(t=>{console.log(t);const a=t.data;this.taskDetail={id:a.taskcode,source:this.getDictText("sourceOptions",a.source),type:this.getDictText("typeOptions",a.type1id),mainCategory:a.type2Name,subCategory:a.type3Name,labz:a.type4id,street:a.streetid,community:this.getDictText("areaOptions",a.areaid),address:a.address,urgent:a.urgent,description:a.eventdesc},this.coordinates=[a.x84,a.y84]}),this.$toast.clear()}catch(t){console.error("获取任务详情失败:",t),this.$toast.fail("获取详情失败")}finally{this.isLoading=!1}},handleTabChange(t){this.activeTab=t},onClickLeft(){this.$router.go(-1)},afterRead(t){this.$toast("图片上传中..."),setTimeout(()=>{this.$toast.success("上传成功")},1e3)},onCancel(){this.$dialog.confirm({title:"提示",message:"确定要取消处理吗？"}).then(()=>{this.$router.go(-1)}).catch(()=>{})},onSubmit(){if(0===this.query.fileStr.length)return this.$toast("请至少上传一张图片");console.log(1111),this.query.opinion.trim()?this.query.contactPhone.trim()?(this.$toast.loading({message:"提交中...",forbidClick:!0,duration:0}),Object(C["b"])({...this.query,operateType:11,eventId:this.$route.query.id}).then(t=>{200==t.code&&(this.$toast.clear(),this.$toast.success("提交成功"),setTimeout(()=>{this.$router.go(-1)},1e3))})):this.$toast("请输入联系方式"):this.$toast("请输入处置结果")},ApplyForReturn(){this.$router.push({name:"ApplyForReturn",query:{id:this.$route.query.id}})},ApplyForExtension(){this.$router.push({name:"ApplyForExtension",query:{id:this.$route.query.id}})}}},L=D,$=(e("20e4"),Object(v["a"])(L,i,s,!1,null,"2ec3985a",null));a["default"]=$.exports},"3ea8":function(t,a,e){"use strict";e("52ef")},"52ef":function(t,a,e){},a29e:function(t,a,e){},ae7d:function(t,a,e){"use strict";e("c0e3")},c0e3:function(t,a,e){}}]);