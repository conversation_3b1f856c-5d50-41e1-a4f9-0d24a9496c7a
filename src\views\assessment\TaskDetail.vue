<template>
  <div class="task-detail">
    <!-- 标题栏 -->
    <van-nav-bar title="任务详情" left-arrow @click-left="onClickLeft" fixed style="position: fixed;"/>

    <!-- 主要内容区域 -->
    <div class="content">
      <van-cell-group :border="false">
        <van-cell title="路段" :value="detail.roadName" title-class="label" value-class="value" />
        <van-cell title="大类" :value="detail.bigIndex" title-class="label" value-class="value" />
        <van-cell title="小类" :value="detail.smallIndex" title-class="label" value-class="value" />
        <van-cell title="问题描述" :value="detail.inText" title-class="label" value-class="value" />
      </van-cell-group>

      <!-- 照片区域 -->
      <div class="photo-section">
        <div class="section-title label">照片</div>
        <div class="photo-list" v-if="detail.images.length > 0">
          <van-image
            v-for="(img, index) in detail.images"
            :key="index"
            :src="img"
            width="100"
            height="100"
            radius="4"
            fit="cover"
          />
        </div>
        <van-empty v-if="detail.images.length === 0" image-size="24" description="暂无照片" />
      </div>

      <!-- 底部信息 -->
      <div class="bottom-info">
        <van-button type="info" block @click="showPopup">已登记{{ totalRecords }}条，点击切换</van-button>
      </div>
    </div>

    <!-- 弹出层 -->
    <van-popup v-model:show="showRecordList" position="bottom" round :style="{ maxHeight: '60%' }">
      <div class="popup-content">
        <!-- <div class="popup-title">切换记录</div> -->
        <van-cell-group>
          <van-cell
            v-for="(record, index) in records"
            :key="index"
            :label="`${record.bigIndex} - ${record.smallIndex}`"
            :title="`${record.roadName}`"
            is-link
            @click="switchRecord(index)"
          />
        </van-cell-group>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { getAssessDetail } from '@/api/assessment'

export default {
  name: 'TaskDetail',
  data() {
    return {
      detail: {
        roadName: '',
        bigIndex: '',
        smallIndex: '',
        inText: '',
        images: [],
      },
      records: [], // 存储所有记录
      showRecordList: false, // 控制弹出层显示
      currentRoadIndex: 0, // 当前选中的路段索引
      currentRecordIndex: 0, // 当前选中的记录索引
    }
  },
  computed: {
    totalRecords() {
      return this.records.length
    },
  },
  created() {
    const id = this.$route.query.id
    if (id) {
      this.getDetail(id)
    }
  },
  methods: {
    onClickLeft() {
      this.$router.back()
    },
    async getDetail(id) {
      try {
        const res = await getAssessDetail({ id })
        if (res.code === 200) {
          // 将所有路段的检查记录展开成一个数组，并添加roadName
          this.records = res.data.roadAssessMinorVOS.reduce((acc, road) => {
            const roadChecks =
              road.roadCheckVOS?.map((check) => ({
                ...check,
                roadName: road.roadName,
              })) || []
            return [...acc, ...roadChecks]
          }, [])

          // 获取第一条记录
          const firstRecord = this.records[0]
          if (firstRecord) {
            this.detail = {
              roadName: firstRecord.roadName,
              bigIndex: firstRecord.bigIndex,
              smallIndex: firstRecord.smallIndex,
              inText: firstRecord.inText,
              images: firstRecord.filePath ? firstRecord.filePath.split(',') : [],
            }
          }
        }
      } catch (error) {
        console.error('获取详情失败:', error)
        this.$toast('获取详情失败')
      }
    },
    showPopup() {
      this.showRecordList = true
    },
    switchRecord(index) {
      const record = this.records[index]
      this.detail = {
        roadName: record.roadName,
        bigIndex: record.bigIndex,
        smallIndex: record.smallIndex,
        inText: record.inText,
        images: record.filePath ? record.filePath.split(',') : [],
      }
      this.currentRecordIndex = index
      this.showRecordList = false
    },
  },
}
</script>

<style lang="scss" scoped>
.task-detail {
  min-height: 100vh;
  background: #f7f8fa;
  padding-top: 46px;

  .content {
    padding: 16px;

    .label {
      font-size: 14px;
      color: #969799;
      width: 80px;
      flex: none !important;
    }

    .value {
      color: #323233;
      text-align: left;
    }

    .photo-section {
      margin-top: 16px;
      background: #fff;
      padding: 16px;
      border-radius: 8px;

      .section-title {
        margin-bottom: 12px;
      }

      .photo-list {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 8px;

        .van-image {
          width: 100%;
          height: 100%;
          aspect-ratio: 1;
        }
      }
    }

    .bottom-info {
      margin-top: 16px;
      padding: 0 16px 16px;
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
    }

    .popup-content {
      padding: 16px 0;

      .popup-title {
        text-align: center;
        font-size: 16px;
        font-weight: 500;
        padding: 0 16px 16px;
        border-bottom: 1px solid #ebedf0;
      }
    }
  }

  :deep(.van-cell) {
    padding: 16px;
    align-items: flex-start;
    line-height: 1.5;
  }

  :deep(.van-cell__value) {
    flex: 1;
  }
}
</style>
