<!--牛皮癣-->
<template>
  <div class="problem-report">
    <!-- 导航栏 -->
    <van-nav-bar title="牛皮癣" left-arrow @click-left="onClickLeft">
      <template #right>
        <div class="nav-right">
          <span class="save-draft">存草稿</span>
          <van-icon name="search" size="18" class="search-icon" />
        </div>
      </template>
    </van-nav-bar>

    <!-- 表单内容 -->
    <div class="form-content">
      <!-- 基本信息 -->
      <van-cell-group class="form-group">
        <!-- 类型选择 -->
        <van-field size="large" label="类型" readonly>
          <template #input>事件</template>
        </van-field>

        <!-- 大类选择 -->
        <van-field size="large" label="大类" readonly>
          <template #input>宣传广告</template>
        </van-field>

        <!-- 小类选择 -->
        <van-field size="large" label="小类" readonly>
          <template #input>非法小广告</template>
        </van-field>

        <!-- 立案标准选择 -->
        <van-field size="large" label="立案标准" readonly>
          <template #input>公共场所内、公告设施上非法张贴、喷涂、手写的各类广告及乱涂乱画现象</template>
        </van-field>
      </van-cell-group>

      <van-cell-group class="form-group">
        <!-- 位置信息 -->
        <van-field size="large" label="位置选择" readonly placeholder="" right-icon="arrow">
          <template #right-icon>
            <div class="section-content">
              <van-button plain type="primary" size="small" class="action-btn" @click="showMapDialog = true">
                选择定位
              </van-button>
            </div>
          </template>
        </van-field>

        <!-- 位置信息 -->
        <van-field size="large" label="位置描述" readonly placeholder="" right-icon="arrow">
          <template #right-icon>
            <div class="section-content">
              <van-button plain type="primary" size="small" class="action-btn">自动生成</van-button>
            </div>
          </template>
        </van-field>

        <van-field
          type="textarea"
          v-model="formData.address"
          label=""
          placeholder="请输入位置描述"
          rows="2"
          autosize
          maxlength="50"
          show-word-limit
        />

        <!-- 问题描述 -->
        <van-field size="large" label="问题描述" readonly placeholder="" right-icon="arrow">
          <template #right-icon>
            <div class="section-content">
              <van-button plain type="primary" size="small" class="action-btn">选择惯用语</van-button>
            </div>
          </template>
        </van-field>
        <van-field
          type="textarea"
          v-model="formData.eventdesc"
          label=""
          placeholder="请输入问题描述"
          rows="2"
          autosize
          maxlength="50"
          show-word-limit
        />
        <van-field
          type="textarea"
          v-model="formData.reporterphone"
          label="手机号码"
          placeholder="请输入手机号码"
          rows="2"
          autosize
          maxlength="50"
          show-word-limit
        />

        <!-- 图片上传 -->
        <div class="form-section">
          <div class="section-label">图片选择（最少一张）</div>
        </div>
        <div class="uploader-wrapper">
          <vantFileUpload v-model="formData.fileStr" :file-type="['jpg', 'png']" @change="handleFileChange" />
        </div>

        <!-- 是否自办结 -->
        <van-field size="large" label="是否自办结" readonly placeholder="" right-icon="arrow">
          <template #right-icon>
            <van-radio-group v-model="formData.isSelfComplete" direction="horizontal">
              <van-radio name="1">是</van-radio>
              <van-radio name="0">否</van-radio>
            </van-radio-group>
          </template>
        </van-field>
      </van-cell-group>
    </div>

    <!-- 提交按钮 -->
    <div class="submit-btn-wrapper">
      <van-button type="info" block @click="onSubmit">提交</van-button>
    </div>
    <!-- 地图弹窗 -->
    <van-popup v-model="showMapDialog" position="bottom" :style="{ height: '80%', width: '100%' }">
      <div class="map-dialog">
        <Map ref="mapComponent" @locationSelected="handleLocationSelected" />
        <div class="map-dialog-footer">
          <van-button type="info" block @click="confirmLocation">确认位置</van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import CustomPicker from '@/components/CustomPicker/index.vue'
import TabSwitch from '@/components/TabSwitch/index.vue'
import Map from '@/components/map/index.vue'
import { addSmallAd } from '@/api/smallAd'
import vantFileUpload from "@/components/vant-file-upload";

export default {
  name: 'ProblemReport',
  components: {
    CustomPicker,
    TabSwitch,
    Map,
    vantFileUpload
  },
  data() {
    return {
      showMapDialog: false, // 新增弹窗控制状态
      // 表单数据
      formData: {
        source: '',
        createid: '',
        type1id: 'npx_wtlx_sj',
        type2id: 'zhcg_npx_dl_xcgg',
        type3id: 'zhcg_npx_dl_ffxgg',
        type4id: 'zhcg_npx_ldbz',
        areaid: '', //定位获取所属区县
        streetid: '', //定位获取所属街道
        address: '', //位置描述
        eventdesc: '', //问题描述
        eventType: 2,
        images: [],
        isSelfComplete: '0',
        reporterphone: '',
        fileStr: '',
      },

      // 选择器数据
      columns: {
        type1Options: [
          { text: '类型1', value: '1' },
          { text: '类型2', value: '2' },
          { text: '类型3', value: '3' },
        ],
        type2Options: [
          { text: '大类1', value: '1' },
          { text: '大类2', value: '2' },
          { text: '大类3', value: '3' },
        ],
        type3Options: [
          { text: '小类1', value: '1' },
          { text: '小类2', value: '2' },
          { text: '小类3', value: '3' },
        ],
        type4Options: [
          { text: '标准1', value: '1' },
          { text: '标准2', value: '2' },
          { text: '标准3', value: '3' },
        ],
      },
    }
  },
  mounted() {
    this.initParams()
  },
  methods: {
    initParams() {
      this.formData.source = '1'
      this.formData.createid = this.user.info.nickName
    },
    // 返回上一页
    onClickLeft() {
      this.$router.go(-1)
    },

    handleLocationSelected({ addressComponent, formatted_address, location }) {
      console.log('location', location)
      this.formData.address = formatted_address
      this.formData.cityid = addressComponent.city_code.slice(3)
      this.formData.cityName = addressComponent.city
      this.formData.areaid = addressComponent.county_code.slice(3)
      this.formData.areaName = addressComponent.county
      this.formData.streetid = addressComponent.town_code.slice(3)
      this.formData.streetName = addressComponent.town
      this.formData.y84 = location.lat
      this.formData.x84 = location.lon
    },
    confirmLocation() {
      this.showMapDialog = false
    },
    // 选择器确认
    onPickerConfirm(type, value) {
      console.log(`${type} selected:`, value)
    },
    // 处理文件变化
    handleFileChange(val) {
      console.log('文件已更新:', val)
      // 如果需要在文件变化时做一些操作，可以在这里处理
      // 例如：自动保存草稿
      if (val && this.savingDraft) {
        // 如果已经有草稿且文件发生变化，可以考虑自动更新草稿
        // 这里只打印信息，实际使用时可以根据需求调用接口
        console.log('文件变化，可以考虑自动更新草稿')
      }
    },

    // 图片上传后处理
    afterRead(file) {
      // 此处可以添加上传逻辑
      console.log(file)
    },

    // 提交表单
    onSubmit() {
      // 表单验证
      if (!this.formData.type1id) {
        return this.$toast('请选择类型')
      }
      if (!this.formData.type2id) {
        return this.$toast('请选择大类')
      }
      if (!this.formData.type3id) {
        return this.$toast('请选择小类')
      }
      if (!this.formData.type4id) {
        return this.$toast('请选择立案标准')
      }
      if (!this.formData.address) {
        return this.$toast('请输入位置描述')
      }
      if (!this.formData.eventdesc) {
        return this.$toast('请输入问题描述')
      }
      if (this.formData.fileStr.length === 0) {
        return this.$toast('请至少上传一张图片')
      }

      // 提交表单
      this.$toast.loading({
        message: '提交中...',
        forbidClick: true,
      })

      addSmallAd(this.formData).then((res) => {
        if (res.code === 200) {
          this.$toast.success('提交成功')
          this.$router.go(-1)
        } else {
          this.$toast.fail('提交失败')
        }
      })

      // 模拟提交
      // setTimeout(() => {
      //   this.$toast.success('提交成功')
      //   this.$router.go(-1)
      // }, 2000)
    },
  },
}
</script>

<style lang="scss" scoped>
.problem-report {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 60px;
}

// 表单内容样式
.form-content {
  padding-bottom: 20px;
}

.form-group {
  background-color: #fff;
  margin-bottom: 10px;
}

// 自定义 van-field 样式
::v-deep .van-field {
  font-size: 14px;
}

::v-deep .van-field__label {
  width: 90px;
  color: #333;
}

::v-deep .van-field__placeholder {
  color: #999;
}

::v-deep .van-field__right-icon {
  color: #ccc;
}

// 表单区块样式
.form-section {
  padding: 0 15px;
  margin-top: 15px;

  .section-label {
    font-size: 14px;
    color: #333;
    margin-bottom: 10px;
  }

  .section-content {
    display: flex;
    justify-content: flex-end;
  }
}

// 按钮样式
.action-btn {
  height: 32px;
  padding: 0 15px;
  font-size: 13px;
  border-radius: 4px;
  background-color: #e8f3ff;
  border-color: #e8f3ff;
  color: #1989fa;
}

// 自动生成文本样式
.auto-text {
  padding: 0 15px;
  font-size: 14px;
  color: #999;
  margin-top: 5px;
}

// 文本域样式
.textarea-wrapper {
  padding: 0 15px;
  margin-top: 10px;
}

.problem-textarea {
  background-color: #fff;
  border-radius: 4px;

  ::v-deep .van-field__control {
    min-height: 80px;
  }
}

// 上传组件样式
.uploader-wrapper {
  padding: 0 15px;

  ::v-deep .van-uploader__upload {
    background-color: #f7f8fa;
    border: 1px dashed #dcdee0;
  }
}

// 是否自办结样式
.self-complete {
  display: flex;
  align-items: center;
  margin-top: 20px;

  .section-label {
    margin-bottom: 0;
    margin-right: 20px;
  }

  .radio-wrapper {
    flex: 1;
  }

  ::v-deep .van-radio {
    margin-right: 20px;
  }

  ::v-deep .van-radio__icon--checked .van-icon {
    background-color: #1989fa;
    border-color: #1989fa;
  }

  ::v-deep .van-radio__icon {
    font-size: 16px;
  }
}

// 提交按钮样式
.submit-btn-wrapper {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 10px 15px;
  background-color: #fff;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);

  ::v-deep .van-button {
    height: 44px;
    line-height: 44px;
    font-size: 16px;
    border-radius: 4px;
  }
}

// 适配 iPhone X 等带底部安全区域的机型
@supports (padding-bottom: constant(safe-area-inset-bottom)) {
  .submit-btn-wrapper {
    padding-bottom: calc(10px + constant(safe-area-inset-bottom));
  }
}

@supports (padding-bottom: env(safe-area-inset-bottom)) {
  .submit-btn-wrapper {
    padding-bottom: calc(10px + env(safe-area-inset-bottom));
  }
}

// 自定义选择器样式
::v-deep .van-field__input {
  .custom-picker {
    width: 100%;
    text-align: right;
  }

  .selected-value {
    color: #323233;
    font-size: 14px;
  }
}

// 新增地图弹窗样式
.map-dialog {
  height: 100%;
  display: flex;
  flex-direction: column;

  &-footer {
    padding: 10px;
    background: #fff;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  }
}

// 调整地图容器高度
.MapContainer {
  height: 0; // 隐藏底部固定地图
}
</style>
