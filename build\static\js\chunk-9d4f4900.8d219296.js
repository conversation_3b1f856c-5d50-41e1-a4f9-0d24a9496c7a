(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-9d4f4900"],{7889:function(t,e,s){},a0f3:function(t,e,s){"use strict";s.d(e,"c",(function(){return a})),s.d(e,"b",(function(){return i})),s.d(e,"d",(function(){return n})),s.d(e,"e",(function(){return r})),s.d(e,"a",(function(){return c}));var o=s("4020");function a(t){return Object(o["a"])({url:"/road/assess/main/list",method:"get",params:t})}function i(t){return Object(o["a"])({url:"/road/assess/main/select",method:"get",params:t})}function n(t){return Object(o["a"])({url:"/road/assess/check/getsubtract",method:"get",params:t})}function r(t){return Object(o["a"])({url:"/road/assess/startcheck/update",method:"put",data:t})}function c(t){return Object(o["a"])({url:"/road/assess/check/add",method:"post",data:t})}},d363:function(t,e,s){"use strict";s("e351")},d7dc:function(t,e,s){"use strict";s.r(e);var o=function(){var t=this,e=t._self._c;return e("div",{staticClass:"assessment-record"},[e("van-nav-bar",{staticStyle:{position:"fixed"},attrs:{title:"考核登记","left-arrow":"",fixed:""},on:{"click-left":t.onClickLeft}}),e("div",{staticClass:"content"},[e("van-cell-group",[e("van-cell",{attrs:{title:"路段",value:t.selectedRoad||"请选择","is-link":""},on:{click:function(e){t.showRoadPicker=!0}}})],1),t.selectedRoad?[e("van-tabs",{model:{value:t.activeTab,callback:function(e){t.activeTab=e},expression:"activeTab"}},t._l(t.subtract,(function(s,o){return e("van-tab",{key:o,attrs:{title:s.indexName}},[e("van-cell-group",t._l(s.roadConfigMinorVOS,(function(o,a){return e("van-cell",{key:a,attrs:{title:o.assessText,"is-link":"",center:""},on:{click:function(e){return t.showViolationSheet(o,s)}},scopedSlots:t._u([{key:"right-icon",fn:function(){return[e("van-button",{attrs:{type:"info",size:"small",plain:""}},[t._v("登记违规")])]},proxy:!0}],null,!0)})})),1)],1)})),1)]:t._e(),t.selectedRoad?e("div",{staticClass:"submit-bar"},[e("div",{staticClass:"recorded-count",on:{click:t.showRecordedList}},[t._v("已登记 "+t._s(t.recordedCount)+" 条")]),e("van-button",{attrs:{type:"info"},on:{click:t.handleSubmit}},[t._v("提交")])],1):t._e()],2),e("van-popup",{attrs:{position:"bottom",round:"","safe-area-inset-bottom":""},model:{value:t.showRoadPicker,callback:function(e){t.showRoadPicker=e},expression:"showRoadPicker"}},[e("van-picker",{attrs:{"show-toolbar":"",columns:t.roads,title:"路段"},on:{confirm:t.onRoadConfirm,cancel:function(e){t.showRoadPicker=!1}}})],1),e("van-popup",{style:{maxHeight:"60%",height:"60%"},attrs:{position:"bottom",round:""},model:{value:t.showRecorded,callback:function(e){t.showRecorded=e},expression:"showRecorded"}},[e("div",{staticClass:"popup-content"},[e("van-nav-bar",{attrs:{title:"已登记记录","left-arrow":""},on:{"click-left":function(e){t.showRecorded=!1}}}),e("van-cell-group",t._l(t.recordedList,(function(s,o){return e("van-cell",{key:o,attrs:{label:`${s.bigIndex} - ${s.smallIndex}`,title:s.inText||"暂无描述"},scopedSlots:t._u([{key:"value",fn:function(){return[e("div",{staticClass:"record-time"},[t._v(t._s(s.timeIn||"-"))])]},proxy:!0}],null,!0)})})),1)],1)]),e("van-popup",{style:{height:"80%"},attrs:{position:"bottom",round:"","safe-area-inset-bottom":""},model:{value:t.showViolationPopup,callback:function(e){t.showViolationPopup=e},expression:"showViolationPopup"}},[e("div",{staticClass:"violation-popup"},[e("van-nav-bar",{attrs:{title:"登记违规","left-arrow":""},on:{"click-left":function(e){t.showViolationPopup=!1}}}),e("div",{staticClass:"violation-content"},[e("van-cell-group",[e("van-cell",{attrs:{"title-class":"title-class",title:"路段",value:t.selectedRoad}}),e("van-cell",{attrs:{"title-class":"title-class",title:"大类",value:t.currentMajorType}}),e("van-cell",{attrs:{"title-class":"title-class",title:"小类",value:t.currentMinorType}})],1),e("van-field",{attrs:{"label-class":"title-class",type:"textarea",placeholder:"请输入问题描述",label:"问题描述",autosize:{minHeight:100,maxHeight:500}},model:{value:t.description,callback:function(e){t.description=e},expression:"description"}}),e("div",{staticClass:"upload-section"},[e("div",{staticClass:"upload-title"},[t._v("图片选择（最少一张）")]),e("van-uploader",{attrs:{"max-count":9,"after-read":t.afterRead,multiple:""},model:{value:t.fileList,callback:function(e){t.fileList=e},expression:"fileList"}},[e("div",{staticClass:"upload-trigger"},[e("van-icon",{attrs:{name:"photograph",size:"24"}})],1)])],1),e("div",{staticClass:"submit-bar"},[e("van-button",{attrs:{type:"info",block:""},on:{click:t.handleSave}},[t._v("保存")])],1)],1)],1)])],1)},a=[],i=(s("e7e5"),s("d399")),n=(s("e17f"),s("2241")),r=(s("14d9"),s("e9f5"),s("f665"),s("ab43"),s("a0f3")),c={name:"AssessmentRecord",data(){return{showRoadPicker:!0,selectedRoad:"",activeTab:0,recordedCount:0,roads:[],detail:null,subtract:[],showViolationPopup:!1,currentMajorType:"",currentMinorType:"",description:"",fileList:[],currentItem:null,selectedRoadId:null,showRecorded:!1,recordedList:[]}},created(){const t=this.$route.query.id;t&&this.getDetail(t).then(e=>{1===e.status&&Object(r["e"])({id:t,status:2})})},methods:{onClickLeft(){this.$router.back()},onRoadConfirm(t){this.selectedRoad=t;const e=this.detail.roadAssessMinorVOS.find(e=>e.roadName===t);console.log("selectRoad",e);const s=e.roadId;this.selectedRoadId=e.id,console.log("roadId",s),this.fetchSubtract(s);const o=this.detail.roadAssessMinorVOS.find(t=>t.id===e.id).roadCheckVOS;console.log("selectVos",o),this.recordedList=o,this.recordedCount=o.length,this.showRoadPicker=!1},async fetchSubtract(t){const e=await Object(r["d"])({roadId:t});200===e.code&&(this.subtract=e.data)},showViolationSheet(t,e){this.currentItem=t,this.currentMajorType=e.indexName,this.currentMinorType=t.assessText,this.showViolationPopup=!0,this.description="",this.fileList=[]},afterRead(t){console.log("文件上传:",t)},handleSave(){if(!this.description.trim())return void this.$toast("请输入问题描述");const t={minorId:this.selectedRoadId,bigIndex:this.currentMajorType,smallIndex:this.currentItem.assessText,num:this.currentItem.num,filePath:null,inText:this.description};Object(r["a"])(t).then(e=>{200===e.code&&(this.$toast.success("保存成功"),this.recordedList.push({...t,timeIn:(new Date).toLocaleString()}))}),this.showViolationPopup=!1,this.recordedCount++},handleSubmit(){n["a"].confirm({title:"",message:"您是最后提交人，是否考核结束?"}).then(()=>{i["a"].loading({duration:0,forbidClick:!0}),Object(r["e"])({id:this.$route.query.id,status:3}).then(t=>{i["a"].clear()})}).catch(()=>{}),this.$toast("提交成功")},getDetail(t){return Object(r["b"])({id:t}).then(t=>{if(200===t.code)return this.detail=t.data,this.roads=t.data.roadAssessMinorVOS.map(t=>t.roadName),t.data}).catch(t=>{console.error("获取详情失败:",t),this.$toast("获取详情失败")})},showRecordedList(){this.showRecorded=!0}}},l=c,d=(s("fb49"),s("d363"),s("2877")),u=Object(d["a"])(l,o,a,!1,null,"6b90ceca",null);e["default"]=u.exports},e351:function(t,e,s){},fb49:function(t,e,s){"use strict";s("7889")}}]);