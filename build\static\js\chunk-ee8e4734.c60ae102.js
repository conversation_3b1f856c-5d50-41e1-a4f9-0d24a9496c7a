(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-ee8e4734"],{3224:function(t,e,i){"use strict";i("f833")},5636:function(t,e,i){},b235:function(t,e,i){"use strict";i("5636")},eeb8:function(t,e,i){"use strict";var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"tab-switch"},[e("van-tabs",{attrs:{border:!1,"line-width":20,"line-height":"2px",color:"#1989fa","title-active-color":"#1989fa","title-inactive-color":"#666"},on:{change:t.handleChange},model:{value:t.modelValue,callback:function(e){t.modelValue=e},expression:"modelValue"}},t._l(t.tabs,(function(t,i){return e("van-tab",{key:i,attrs:{title:t.label,name:t.value}})})),1)],1)},s=[],n={name:"TabSwitch",props:{tabs:{type:Array,default:()=>[]},value:{type:[String,Number],default:""}},computed:{modelValue:{get(){return this.value},set(t){this.$emit("input",t)}}},methods:{handleChange(t){this.$emit("change",t)}}},o=n,l=(i("3224"),i("2877")),r=Object(l["a"])(o,a,s,!1,null,"185e5f8f",null);e["a"]=r.exports},f833:function(t,e,i){},f985:function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"todo-page"},[e("van-nav-bar",{attrs:{title:"草稿箱","left-arrow":"",fixed:""},on:{"click-left":t.onClickLeft}}),e("van-pull-refresh",{on:{refresh:t.onRefresh},model:{value:t.refreshing,callback:function(e){t.refreshing=e},expression:"refreshing"}},[e("van-list",{attrs:{finished:t.finished,"finished-text":"没有更多了"},on:{load:t.onLoad},model:{value:t.loading,callback:function(e){t.loading=e},expression:"loading"}},[e("div",{staticClass:"todo-list"},t._l(t.List,(function(i,a){return e("van-cell-group",{key:a},[e("van-cell",{staticClass:"todo-item",on:{click:function(e){return t.getDraftDetail(i)}}},[e("div",{staticClass:"todo-content"},[e("div",{staticClass:"todo-container"},[e("div",{staticClass:"todo-container-left"},[e("van-image",{attrs:{width:"68",height:"68",src:i.image,fit:"cover",radius:"5"}})],1),e("div",{staticClass:"todo-container-right"},[e("div",{staticClass:"todo-title"},[t._v(" "+t._s(i.title)+" ")]),e("div",{staticClass:"todo-info"},[e("span",{staticClass:"time"},[t._v(t._s(i.time))])])])])])])],1)})),1)])],1)],1)},s=[],n=(i("14d9"),i("e9f5"),i("ab43"),i("eeb8")),o=i("2934"),l={name:"index",components:{tabSwitch:n["a"]},data(){return{queryParams:{pageSize:10,pageNum:1},List:[],refreshing:!1,loading:!1,finished:!1,total:0,isLoading:!1}},methods:{async getList(){if(!this.isLoading)try{this.isLoading=!0,this.$toast.loading({message:"加载中...",forbidClick:!0,duration:0});const t=await Object(o["l"])({...this.queryParams,isDraft:1});this.total=t.total,this.refreshing&&(this.List=[]),this.List.push(...t.rows.map(t=>({id:t.id,title:t.type4id,time:t.createtime,image:this.$getImageUrl(t.fileStr)}))),this.finished=this.List.length>=this.total,this.loading=!1,this.refreshing&&(this.refreshing=!1),this.$toast.clear()}catch(t){console.error("获取列表失败:",t),this.$toast.fail("获取列表失败"),this.$toast.clear()}finally{this.isLoading=!1}},getDraftDetail(t){this.$router.push({name:"DraftDetail",query:{id:t.id}})},async onRefresh(){this.queryParams.pageNum=1,this.finished=!1,await this.getList()},onLoad(){this.queryParams.pageNum+=1,this.getList()},onClickLeft(){this.$router.go(-1)}},mounted(){this.getList()}},r=l,c=(i("b235"),i("2877")),h=Object(c["a"])(r,a,s,!1,null,"f2e1934e",null);e["default"]=h.exports}}]);