<template>
  <div class="violation-record">
    <!-- 标题栏 -->
    <van-nav-bar
      title="登记违规"
      left-arrow
      @click-left="onClickLeft"
      fixed
    />

    <!-- 主要内容区域 -->
    <div class="content">
      <!-- 信息展示 -->
      <van-cell-group>
        <van-cell title-class="title-class" title="路段" :value="road" />
        <van-cell title-class="title-class" title="大类" :value="majorType" />
        <van-cell title-class="title-class" title="小类" :value="minorType" />
      </van-cell-group>

      <!-- 问题描述 -->
      <van-field
        v-model="description"
        type="textarea"
        placeholder="请输入"
        label="问题描述"
        :autosize="{ minHeight: 100, maxHeight: 500 }"
      />

      <!-- 图片上传 -->
      <div class="upload-section">
        <div class="upload-title">图片选择（最少一张）</div>
        <van-uploader
          v-model="fileList"
          :max-count="9"
          :after-read="afterRead"
          multiple
        >
          <div class="upload-trigger">
            <van-icon name="photograph" size="24" />
          </div>
        </van-uploader>
      </div>

      <!-- 底部保存按钮 -->
      <div class="submit-bar">
        <van-button type="primary" block @click="handleSave">
          保存
        </van-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ViolationRecord',
  data() {
    return {
      road: '',
      majorType: '',
      minorType: '',
      description: '',
      fileList: [],
    }
  },
  created() {
    // 从路由参数中获取数据
    const { road, majorType, minorType } = this.$route.query
    this.road = road
    this.majorType = majorType
    this.minorType = minorType
  },
  methods: {
    onClickLeft() {
      this.$router.back()
    },
    afterRead(file) {
      // 处理图片上传后的逻辑
      console.log('文件上传:', file)
    },
    handleSave() {
      // 表单验证
      if (!this.description.trim()) {
        this.$toast('请输入问题描述')
        return
      }
      if (this.fileList.length === 0) {
        this.$toast('请至少上传一张图片')
        return
      }

      // TODO: 处理保存逻辑
      this.$toast.success('保存成功')
      this.$router.back()
    }
  }
}
</script>

<style lang="scss" scoped>
.violation-record {
  min-height: 100vh;
  background: #f7f8fa;
  padding-top: 46px;

  .content {
    padding: 16px;
    padding-bottom: 80px;

    .upload-section {
      margin-top: 16px;
      background: #fff;
      padding: 16px;
      border-radius: 8px;

      .upload-title {
        margin-bottom: 16px;
        color: #323233;
        font-size: 14px;
      }

      .upload-trigger {
        width: 100px;
        height: 100px;
        background: #f7f8fa;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
      }
    }
  }

  .submit-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    padding: 10px 16px;
    box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.05);
  }
}
</style> 
<style>
.title-class {
  font-size: 14px;
  color: #323233;
  width: 100px;
  flex: none !important;
}
</style>