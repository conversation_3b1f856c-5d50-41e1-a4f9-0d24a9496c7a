(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-a03275ea"],{"46ec":function(t,a,i){"use strict";i("5fe1")},"51f7":function(t,a,i){"use strict";i("66d3")},"5fe1":function(t,a,i){},"66d3":function(t,a,i){},ba7a:function(t,a,i){"use strict";i("e4d9")},e4d9:function(t,a,i){},f923:function(t,a,i){"use strict";i.r(a);var e=function(){var t=this,a=t._self._c;return a("div",{staticClass:"task-detail-page"},[a("van-nav-bar",{attrs:{title:"待办任务","left-arrow":"",fixed:""},on:{"click-left":t.onClickLeft}}),a("div",{staticClass:"tab-fixed"},[a("tabSwitch",{attrs:{tabs:t.tabOptions},on:{change:t.handleTabChange},model:{value:t.activeTab,callback:function(a){t.activeTab=a},expression:"activeTab"}})],1),t.taskDetail?a("div",{staticClass:"detail-content"},["基本信息"===t.activeTab?a("basic-information",{attrs:{"task-detail":t.taskDetail,"is-agree":t.query.isAgree,opinion:t.query.opinion,"file-str":t.query.fileStr,coordinates:t.coordinates},on:{"update:isAgree":function(a){return t.$set(t.query,"isAgree",a)},"update:is-agree":function(a){return t.$set(t.query,"isAgree",a)},"update:opinion":function(a){return t.$set(t.query,"opinion",a)},"update:fileStr":function(a){return t.$set(t.query,"fileStr",a)},"update:file-str":function(a){return t.$set(t.query,"fileStr",a)},upload:t.afterRead}}):t._e(),"流程跟踪"===t.activeTab?a("flow-tracing"):t._e()],1):t._e(),a("div",{staticClass:"bottom-btns"},[a("van-button",{staticClass:"cancel-btn",attrs:{plain:""},on:{click:t.onCancel}},[t._v("取消")]),a("van-button",{staticClass:"submit-btn",attrs:{type:"info"},on:{click:t.onSubmit}},[t._v("提交")])],1)],1)},s=[],o=(i("e9f5"),i("ab43"),i("eeb8")),l=function(){var t=this,a=t._self._c;return a("div",{staticClass:"basic-info"},[a("div",{staticClass:"info-item special"},[a("div",{staticClass:"item-label"},[t.taskDetail.urgent&&1==t.taskDetail.urgent?a("van-tag",{staticClass:"dangerBtn",attrs:{type:"danger",plain:""}},[t._v("紧急")]):t._e(),a("span",{staticClass:"num-label"},[t._v(t._s(t.taskDetail.id||"暂无数据"))])],1)]),a("div",{staticClass:"info-list"},[a("div",{staticClass:"info-item"},[a("div",{staticClass:"item-label"},[t._v("问题来源")]),a("div",{staticClass:"item-value"},[t._v(t._s(t.taskDetail.source||"暂无数据"))])]),a("div",{staticClass:"info-item"},[a("div",{staticClass:"item-label"},[t._v("问题类型")]),a("div",{staticClass:"item-value"},[t._v(t._s(t.taskDetail.type||"暂无数据"))])]),a("div",{staticClass:"info-item"},[a("div",{staticClass:"item-label"},[t._v("大类名称")]),a("div",{staticClass:"item-value"},[t._v(t._s(t.taskDetail.mainCategory||"暂无数据"))])]),a("div",{staticClass:"info-item"},[a("div",{staticClass:"item-label"},[t._v("小类名称")]),a("div",{staticClass:"item-value"},[t._v(t._s(t.taskDetail.subCategory||"暂无数据"))])]),a("div",{staticClass:"info-item"},[a("div",{staticClass:"item-label"},[t._v("立案标准")]),a("div",{staticClass:"item-value"},[t._v(t._s(t.taskDetail.labz||"暂无数据"))])]),a("div",{staticClass:"info-item"},[a("div",{staticClass:"item-label"},[t._v("所属区县")]),a("div",{staticClass:"item-value"},[t._v(t._s(t.taskDetail.community||"暂无数据"))])]),a("div",{staticClass:"info-item"},[a("div",{staticClass:"item-label"},[t._v("所属街道")]),a("div",{staticClass:"item-value"},[t._v(t._s(t.taskDetail.street||"暂无数据"))])]),a("div",{staticClass:"info-item"},[a("div",{staticClass:"item-label"},[t._v("事发地址")]),a("div",{staticClass:"item-value"},[t._v(t._s(t.taskDetail.address||"暂无数据"))])]),a("div",{staticClass:"info-item"},[a("div",{staticClass:"item-label"},[t._v("问题描述")]),a("div",{staticClass:"item-value"},[t._v(t._s(t.taskDetail.description||"暂无数据"))])])]),a("div",{staticClass:"map-location"},[a("div",{staticClass:"map-container",on:{click:function(a){t.showMapDialog=!0}}},[a("img",{staticClass:"map-image",attrs:{src:i("a5d1"),alt:"地图位置"}}),a("div",{staticClass:"map-marker"},[a("van-icon",{attrs:{name:"location-o",size:"24",color:"#1989fa"}}),a("div",{staticClass:"marker-label"},[t._v("点击查看点位")])],1)])]),a("div",{staticClass:"bottom-qa"},[a("div",{staticClass:"qa-title"},[t._v("以上信息是否属实?")]),a("div",{staticClass:"qa-options"},[a("van-radio-group",{attrs:{direction:"horizontal"},on:{change:t.updateCorrectLocation},model:{value:t.isAgreeLocal,callback:function(a){t.isAgreeLocal=a},expression:"isAgreeLocal"}},[a("van-radio",{attrs:{name:2}},[t._v("否")]),a("van-radio",{attrs:{name:1}},[t._v("是")])],1)],1)]),a("div",{staticClass:"attachment-area"},[a("div",{staticClass:"attachment-label"},[t._v("现场图片")]),a("div",{staticClass:"upload-placeholder"},[a("vantFileUpload",{attrs:{"file-type":["jpg","png"]},on:{change:t.updateFileStr},model:{value:t.fileStrLocal,callback:function(a){t.fileStrLocal=a},expression:"fileStrLocal"}})],1)]),a("div",{staticClass:"remark-area"},[a("div",{staticClass:"remark-label-container"},[a("div",{staticClass:"remark-label"},[t._v("问题说明")]),a("idioms-selector",{on:{select:t.selectIdiom}},[t._v("选择惯用语")])],1),a("van-field",{attrs:{type:"textarea",placeholder:"请输入说明内容",rows:"1",autosize:""},on:{input:t.updateRemarkContent},model:{value:t.opinionLocal,callback:function(a){t.opinionLocal=a},expression:"opinionLocal"}})],1),a("van-popup",{style:{height:"80%",width:"100%"},attrs:{position:"bottom"},model:{value:t.showMapDialog,callback:function(a){t.showMapDialog=a},expression:"showMapDialog"}},[a("div",{staticClass:"map-dialog"},[t.showMapDialog?a("Map",{ref:"mapComponent",attrs:{coordinates:t.coordinates},on:{locationSelected:t.handleLocationSelected}}):t._e(),a("div",{staticClass:"map-dialog-footer"},[a("van-button",{attrs:{type:"primary",block:""},on:{click:t.confirmLocation}},[t._v("返回")])],1)],1)])],1)},n=[],c=i("6e29"),r=i("5945"),d=i("207e"),u={name:"BasicInformation",components:{IdiomsSelector:c["a"],Map:r["a"],vantFileUpload:d["a"]},props:{taskDetail:{type:Object,default:()=>({})},isAgree:{type:Number,default:1},opinion:{type:String,default:""},fileStr:{type:String,default:""},coordinates:{type:Array,default:()=>[]}},data(){return{showMapDialog:!1,isAgreeLocal:this.isAgree,opinionLocal:this.opinion,fileStrLocal:this.fileStr,areaOptions:[]}},watch:{isAgree(t){this.isAgreeLocal=t},opinion(t){this.opinionLocal=t},fileStr(t){this.fileStrLocal=t}},methods:{getDictList(){this.getDicts("county").then(t=>{this.areaOptions=t.data.map(t=>({label:t.dictLabel,value:t.dictValue}))})},onUpload(t){this.$emit("upload",t)},updateCorrectLocation(t){this.$emit("update:isAgree",t)},updateRemarkContent(t){this.$emit("update:opinion",t)},updateFileStr(t){this.$emit("update:fileStr",t)},selectIdiom(t){this.opinionLocal=t,this.updateRemarkContent(t)},handleLocationSelected(t){},confirmLocation(){this.showMapDialog=!1}}},p=u,v=(i("ba7a"),i("2877")),m=Object(v["a"])(p,l,n,!1,null,"a72cd6d6",null),h=m.exports,f=function(){var t=this,a=t._self._c;return a("div",{staticClass:"process-tracking"},[a("div",{staticClass:"timeline"},t._l(t.processHistory,(function(i,e){return a("div",{key:e,staticClass:"timeline-item"},[a("div",{staticClass:"timeline-left"},[a("div",{staticClass:"timeline-dot",class:{active:!0}}),e!==t.processHistory.length-1?a("div",{staticClass:"timeline-line"}):t._e()]),a("div",{staticClass:"timeline-content"},[a("div",{staticClass:"timeline-header"},[a("div",{staticClass:"timeline-header-left"},[a("span",{staticClass:"timeline-time"},[t._v(t._s(i.name))]),a("span",{staticClass:"timeline-time"},[t._v(t._s(i.time))])]),a("span",{staticClass:"timeline-location"},[t._v(t._s(i.departName))])]),t._l(i.list,(function(i,e){return a("div",{staticClass:"timeline-detail"},[a("p",[t._v("执行的操作："+t._s(i.content))]),a("p",[t._v("接收人："+t._s(i.person))]),a("p",[t._v("批转意见：")]),a("p",[t._v(t._s(i.opinion))])])}))],2)])})),0)])},g=[],C=(i("910d"),i("2934")),b={name:"FlowTracing",data(){return{processHistory:[]}},mounted(){this.getProcessHistory()},methods:{getProcessHistory(){Object(C["f"])({eventId:this.$route.query.id}).then(t=>{this.processHistory=t.data.map(t=>({name:this.getNodeType(t.type),time:t.time,departName:t.departName,list:t.list.map(t=>({content:t.operateName,person:t.operatePerson,opinion:t.opinion?t.opinion:null}))})).filter(t=>t.departName)})},getNodeType(t){const a={1:"上报",2:"立案",3:"派遣",4:"处置",5:"核查",6:"结案"};return a[t]||""}}},_=b,y=(i("51f7"),Object(v["a"])(_,f,g,!1,null,"f71956a0",null)),k=y.exports,D={name:"ToDoTasksDetail",components:{tabSwitch:o["a"],BasicInformation:h,FlowTracing:k},data(){return{activeTab:"基本信息",tabOptions:[{label:"基本信息",value:"基本信息"},{label:"流程跟踪",value:"流程跟踪"}],query:{isAgree:1,opinion:"",fileStr:""},taskDetail:null,isLoading:!1,areaOptions:[],typeOptions:[],sourceOptions:[],coordinates:[]}},mounted(){this.init()},methods:{async init(){await this.getDictsList(),await this.getTaskDetail()},async getDictsList(){return Promise.all([new Promise(t=>{this.getDicts("zhcg_wtlx").then(a=>{this.typeOptions=a.data.map(t=>({label:t.dictLabel,value:t.dictValue})),t()})}),new Promise(t=>{this.getDicts("zhcg_wtly").then(a=>{this.sourceOptions=a.data.map(t=>({label:t.dictLabel,value:t.dictValue})),t()})}),new Promise(t=>{this.getDicts("county").then(a=>{this.areaOptions=a.data.map(t=>({label:t.dictLabel,value:t.dictValue})),t()})})])},async getTaskDetail(){try{this.isLoading=!0,this.$toast.loading({message:"加载中...",forbidClick:!0,duration:0});const t=await Object(C["k"])(this.$route.query.id);console.log("ddd",t);const a=t.data;this.taskDetail={id:a.taskcode,source:this.getDictText("sourceOptions",a.source),type:this.getDictText("typeOptions",a.type1id),mainCategory:a.type2Name,subCategory:a.type3Name,labz:a.type4id,street:a.streetid,community:this.getDictText("areaOptions",a.areaid),address:a.address,urgent:a.urgent,description:a.eventdesc},this.coordinates=[a.x84,a.y84],this.$toast.clear()}catch(t){console.error("获取任务详情失败:",t),this.$toast.fail("获取详情失败")}finally{this.isLoading=!1}},handleTabChange(t){this.activeTab=t},onClickLeft(){this.$router.go(-1)},afterRead(t){this.$toast("图片上传中..."),setTimeout(()=>{this.$toast.success("上传成功")},1e3)},onCancel(){this.$dialog.confirm({title:"提示",message:"确定要取消处理吗？"}).then(()=>{this.$router.go(-1)}).catch(()=>{})},onSubmit(){if(0===this.query.fileStr.length)return this.$toast("请至少上传一张图片");this.query.opinion.trim()?(this.$toast.loading({message:"提交中...",forbidClick:!0,duration:0}),Object(C["b"])({...this.query,operateType:Number(this.$route.query.operateType),eventId:this.$route.query.id}).then(t=>{200==t.code&&(this.$toast.clear(),this.$toast.success("提交成功"),setTimeout(()=>{this.$router.go(-1)},1e3))})):this.$toast("请输入问题说明")}}},L=D,w=(i("46ec"),Object(v["a"])(L,e,s,!1,null,"6450a0ee",null));a["default"]=w.exports}}]);