(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-193aab03"],{"0222":function(t,e,a){"use strict";a("640c")},"1b90":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"todo-page"},[e("van-nav-bar",{attrs:{title:"专项整治","left-arrow":"",fixed:""},on:{"click-left":t.onClickLeft}}),e("div",{staticClass:"tab-fixed"},[e("tabSwitch",{attrs:{tabs:t.tabOptions},on:{change:t.handleTabChange},model:{value:t.queryParams.activeTab,callback:function(e){t.$set(t.queryParams,"activeTab",e)},expression:"queryParams.activeTab"}})],1),e("van-pull-refresh",{on:{refresh:t.onRefresh},model:{value:t.refreshing,callback:function(e){t.refreshing=e},expression:"refreshing"}},[e("van-list",{attrs:{finished:t.finished,"finished-text":"没有更多了"},on:{load:t.onLoad},model:{value:t.loading,callback:function(e){t.loading=e},expression:"loading"}},[e("div",{staticClass:"todo-list"},t._l(t.List,(function(a,i){return e("van-cell-group",{key:i},[e("van-cell",{staticClass:"todo-item",on:{click:function(e){return t.getTaskDetail(a)}}},[e("div",{staticClass:"todo-content"},[e("div",{staticClass:"source-info"},[e("div",{staticClass:"source-info-left"},[e("div",{staticClass:"source-icon"},[e("van-icon",{attrs:{name:t.titleIcon,size:"30"}})],1),e("span",{staticClass:"source-value"},[t._v(t._s(a.taskName))])]),1==t.queryParams.activeTab?e("div",{staticClass:"source-info-right"},[e("van-button",{staticClass:"rlBtn",attrs:{type:"info"},on:{click:function(e){return e.stopPropagation(),t.getTaskDetail(a)}}},[t._v("发布")])],1):t._e()]),e("div",{staticClass:"todo-container"},[e("div",{staticClass:"todo-container-left"},[e("van-image",{attrs:{width:"68",height:"68",src:a.image,fit:"cover",radius:"5"}})],1),e("div",{staticClass:"todo-container-right"},[e("div",{staticClass:"todo-title"},[t._v(" "+t._s(a.title)+" ")]),e("div",{staticClass:"todo-info"},[e("span",{staticClass:"time"},[t._v(t._s(a.time))])]),e("div",{staticClass:"todo-title"},[t._v(" "+t._s(a.content)+" ")]),e("div",{staticClass:"countdown"},[a.urgent?[e("span",{staticClass:"urgent-tag"},[t._v("紧急")])]:t._e()],2)])])])])],1)})),1)])],1)],1)},s=[],n=(a("14d9"),a("e9f5"),a("ab43"),a("eeb8")),r=a("2934"),o=a("4260"),l={name:"index",components:{tabSwitch:n["a"]},data(){return{queryParams:{activeTab:1,pageSize:10,pageNum:1},tabOptions:[{label:"任务列表",value:1},{label:"上报案卷",value:2}],List:[],titleIcon:a("6f8e"),refreshing:!1,loading:!1,finished:!1,total:0,isLoading:!1,typeData:[{value:2,label:"普查"},{value:1,label:"整治"}],sourceOptions:[]}},methods:{async getList(){if(!this.isLoading)try{this.isLoading=!0,this.$toast.loading({message:"加载中...",forbidClick:!0,duration:0});const t=1==this.queryParams.activeTab?await Object(r["n"])({pageNum:this.queryParams.pageNum,pageSize:this.queryParams.pageSize}):await Object(r["l"])({pageNum:this.queryParams.pageNum,pageSize:this.queryParams.pageSize,createUserId:this.user.userId,specialTaskId:-1});if(console.log(t,"getSpecialTaskList"),200===t.code){this.total=t.total;const e=(t.rows||[]).map(t=>1==this.queryParams.activeTab?{id:t.id,image:t.fileStr?Object(o["c"])(t.fileStr.split(",")[0]):"",title:this.getDictText("typeData",t.taskType),taskName:t.taskTitle,time:t.createTime,content:t.taskDesc,urgent:1===t.taskLevel,rawData:t}:{id:t.id,image:t.fileStr?Object(o["c"])(t.fileStr.split(",")[0]):"",title:t.type2Name+"-"+t.type3Name,taskName:t.source?this.getDictText("sourceOptions",t.source):"",time:t.createtime,content:t.eventdesc,urgent:1==t.urgent,rawData:t});this.refreshing&&(this.List=[]),this.List.push(...e),this.finished=this.List.length>=this.total}else this.$toast.fail(t.msg||"获取列表失败");this.loading=!1,this.refreshing&&(this.refreshing=!1),this.$toast.clear()}catch(t){console.error("获取列表失败:",t),this.$toast.fail("获取列表失败"),this.$toast.clear(),this.loading=!1,this.refreshing&&(this.refreshing=!1)}finally{this.isLoading=!1}},getTaskDetail(t){1==this.queryParams.activeTab?this.$router.push({name:"SpecialRectificationDetail",query:{id:t.id}}):t.rawData&&t.rawData.isShow?1===parseInt(t.rawData.isShow)?this.$router.push({name:"SpecialRectificationSubmitZz",query:{detailId:t.id}}):2===parseInt(t.rawData.isShow)&&this.$router.push({name:"SpecialRectificationSubmit",query:{detailId:t.id}}):this.$toast("无法确定详情类型")},getDictList(){this.getDicts("zhcg_wtly").then(t=>{this.sourceOptions=t.data.map(t=>({label:t.dictLabel,value:t.dictValue}))})},async onRefresh(){this.queryParams.pageNum=1,this.finished=!1,await this.getList()},onLoad(){this.queryParams.pageNum+=1,this.getList()},handleTabChange(t){this.queryParams.pageNum=1,this.List=[],this.finished=!1,this.getList()},onClickLeft(){this.$router.go(-1)}},mounted(){this.getDictList(),this.getList()}},c=l,u=(a("0222"),a("2877")),h=Object(u["a"])(c,i,s,!1,null,"7488b130",null);e["default"]=h.exports},3224:function(t,e,a){"use strict";a("f833")},"640c":function(t,e,a){},"6f8e":function(t,e){t.exports="data:image/png;base64,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"},eeb8:function(t,e,a){"use strict";var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"tab-switch"},[e("van-tabs",{attrs:{border:!1,"line-width":20,"line-height":"2px",color:"#1989fa","title-active-color":"#1989fa","title-inactive-color":"#666"},on:{change:t.handleChange},model:{value:t.modelValue,callback:function(e){t.modelValue=e},expression:"modelValue"}},t._l(t.tabs,(function(t,a){return e("van-tab",{key:a,attrs:{title:t.label,name:t.value}})})),1)],1)},s=[],n={name:"TabSwitch",props:{tabs:{type:Array,default:()=>[]},value:{type:[String,Number],default:""}},computed:{modelValue:{get(){return this.value},set(t){this.$emit("input",t)}}},methods:{handleChange(t){this.$emit("change",t)}}},r=n,o=(a("3224"),a("2877")),l=Object(o["a"])(r,i,s,!1,null,"185e5f8f",null);e["a"]=l.exports},f833:function(t,e,a){}}]);