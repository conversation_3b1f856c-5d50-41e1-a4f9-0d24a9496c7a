(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-cc976c08"],{"5f29":function(e,t,o){},"7a45":function(e,t,o){e.exports=o.p+"static/img/loginBg.4eb74c4f.png"},"7ded":function(e,t,o){"use strict";o.d(t,"c",(function(){return i})),o.d(t,"a",(function(){return a})),o.d(t,"b",(function(){return r}));var n=o("4020");function i(e){return Object(n["a"])({url:"/login",method:"post",data:e})}function a(){return Object(n["a"])({url:"/captchaImage",method:"get"})}function r(){return Object(n["a"])({url:"/getInfo",method:"get"})}},"9ed6":function(e,t,o){"use strict";o.r(t);var n=function(){var e=this,t=e._self._c;return t("div",{staticClass:"login-container"},[e._m(0),t("div",{staticClass:"login-form"},[t("van-form",{on:{submit:e.onSubmit}},[t("van-field",{attrs:{placeholder:"请输入账号",rules:[{required:!0,message:"请输入账号"}]},scopedSlots:e._u([{key:"left-icon",fn:function(){return[t("van-icon",{attrs:{name:"user-o"}})]},proxy:!0}]),model:{value:e.loginForm.username,callback:function(t){e.$set(e.loginForm,"username",t)},expression:"loginForm.username"}}),t("van-field",{attrs:{type:"password",placeholder:"请输入密码",rules:[{required:!0,message:"请输入密码"}]},scopedSlots:e._u([{key:"left-icon",fn:function(){return[t("van-icon",{attrs:{name:"lock"}})]},proxy:!0}]),model:{value:e.loginForm.password,callback:function(t){e.$set(e.loginForm,"password",t)},expression:"loginForm.password"}}),t("van-field",{attrs:{placeholder:"请输入验证码",rules:[{required:!0,message:"请输入验证码"}]},scopedSlots:e._u([{key:"left-icon",fn:function(){return[t("van-icon",{attrs:{name:"shield-o"}})]},proxy:!0},{key:"right-icon",fn:function(){return[t("img",{staticClass:"captcha-img",attrs:{src:e.codeUrl,alt:"验证码"},on:{click:e.getCode}})]},proxy:!0}]),model:{value:e.loginForm.code,callback:function(t){e.$set(e.loginForm,"code",t)},expression:"loginForm.code"}}),t("div",{staticClass:"form-btn"},[t("van-button",{attrs:{round:"",block:"",type:"info","native-type":"submit"}},[e._v(" 登录 ")])],1)],1)],1)])},i=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"login-banner"},[t("img",{attrs:{src:o("7a45"),alt:"登录banner"}})])}],a=(o("14d9"),o("7ded")),r=o("0a5a"),s={name:"Login",data(){return{loginForm:{username:"",password:"",code:"",uuid:""},codeUrl:""}},created(){this.getCode()},methods:{async getCode(){const e=await Object(a["a"])();this.codeUrl="data:image/gif;base64,"+e.img,this.loginForm.uuid=e.uuid},async onSubmit(){Object(a["c"])(this.loginForm).then(e=>{Object(r["c"])(e.token),this.GetInfo(e.token),this.$toast.success("登录成功"),this.$router.push("/")}).catch(e=>{this.$toast.fail(e.msg),this.getCode()})},GetInfo(e){Object(a["b"])(e).then(e=>{var t,o,n;const i=e.user,a={deptName:(null===i||void 0===i||null===(t=i.dept)||void 0===t?void 0:t.deptName)||"",deptId:(null===i||void 0===i||null===(o=i.dept)||void 0===o?void 0:o.deptId)||"",nickName:i.nickName,...i,id:(null===e||void 0===e||null===(n=e.dogUserQualifi)||void 0===n?void 0:n.id)||"",realName:i.userName||"",mobile:i.phonenumber||"",userQualifi:(null===e||void 0===e?void 0:e.dogUserQualifi)||void 0,qualifi:(null===e||void 0===e?void 0:e.dogQualifi)||void 0,roleType:e.roleType};this.vuex("user",a)})}}},c=s,l=(o("aef0"),o("2877")),u=Object(l["a"])(c,n,i,!1,null,"d20e130c",null);t["default"]=u.exports},aef0:function(e,t,o){"use strict";o("5f29")}}]);