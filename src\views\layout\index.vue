<template>
  <div>
      <router-view />
<!--    <van-tabbar v-model="active" active-color="#4689F5" inactive-color="#AEBAC7" style="z-index: 9000;">-->
<!--      <van-tabbar-item icon-prefix="icon" icon="home_o" to="/">首页</van-tabbar-item>-->
<!--      <van-tabbar-item icon-prefix="icon" icon="map_o" to="/map">我要参加</van-tabbar-item>-->
<!--      <van-tabbar-item icon-prefix="icon" icon="mine_o" to="/mine">我的</van-tabbar-item>-->
<!--    </van-tabbar>-->
  </div>
</template>

<script>

export default {
  name: 'LayoutIndex',
  components: {},
  props: {},
  data() {
    return {
      active: 0
    }
  },
  computed: {

  },
  watch: {
    '$route'(val) {
      const name = val.name
      if (name == 'Home' && this.active != 0) {
        this.active = 0
      } else if (name == 'Map' && this.active != 1) {
        this.active = 1
      } else if (name == 'Mine' && this.active != 2) {
        this.active = 2
      }
    }
  },
  mounted() {

  },
  created() {},
  methods: {}
}
</script>
