<template>
  <div class="container">
    <!-- 头部区域 -->
    <div class="container-head">
      <div class="head-right">
        <div class="personal-center" @click="goPersonCenter">
          <i class="icon-user"></i>
          <span>个人中心</span>
          <i class="icon-arrow"></i>
        </div>
      </div>
    </div>

    <!-- 统计数据区域 -->
    <div class="stats-container">
      <!-- 标签切换 -->
      <div class="tab-switch">
        <van-tabs
          v-model="activeTab"
          :border="false"
          :line-width="20"
          line-height="3px"
          color="#1989fa"
          title-active-color="#1989fa"
          title-inactive-color="#666"
          :animated="true"
          :duration="0.3"
          :swipeable="true"
          @change="switchTab"
        >
          <van-tab
            v-for="(item,i) in TabList"
            :key="i"
            :title="item.name"
            :name="item.value"
          >
          </van-tab>
        </van-tabs>
      </div>

      <!--指标数值-->
      <div class="index-List">
        <div class="index-List-item" v-for="(item,i) in indexList">
          <div class="index-List-item-number">{{item.name}}</div>
          <div class="index-List-item-text">{{item.value}}</div>
        </div>
      </div>

      <div class="index-base"></div>

      <!-- 问题上报卡片 -->
      <div class="card card-report">
        <div class="card-left">
          <div class="card-title">问题上报</div>
          <div class="card-desc">上报巡逻中遇到的问题</div>
        </div>
        <div class="card-right">
          <div class="btn-register" @click="$router.push('/problem')">登记 ></div>
        </div>
      </div>
    </div>

    <!-- 功能卡片区域 -->
    <div class="card-container">
      <div class="card-list">
        <div class="card card-small" @click="item.path?$router.push(item.path):false" v-for="(item,i) in currentMenuList" :key="i">
          <div class="card-content">
            <div class="card-icon" :style="{background: 'url(' + item.iconAssets + ') no-repeat'}">
            </div>
            <div class="card-info">
              <div class="card-title">{{ item.name }}</div>
              <div class="card-status">
                <span>{{ item.text }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {getAPPList, getDeptCount, getJdyToDoTask, getYdMenu, getYdStatistics} from "@/api/common";
import {getInfo} from "@/api/login";
import { getToken } from "@/util/auth";

export default {
  name: "Home",
  components: {},
  props: {},
  data() {
    return {
      TabList: [{name:'本日',value:1},{name:'本月',value:2}],
      activeTab: 1,
      indexList: [],
      AllMenuList: [
        {
          name: "考勤打卡",
          text: "已签到",
          path: "/clockIn",
          iconAssets: require("@/assets/home/<USER>"),
          key:"kqdk"
        },
        {
          name: "待办任务",
          text: "核查: 0 核实: 0",
          path: "/ToDoTasks",
          iconAssets: require("@/assets/home/<USER>"),
          key:"dbrw"
        },
        {
          name: "已办任务",
          text: "个人已办任务",
          path: "/CompletedTasks",
          iconAssets: require("@/assets/home/<USER>"),
          key:"ybrw"
        },
        {
          name: "工作统计",
          text: "案件处理情况统计",
          path: "",
          iconAssets: require("@/assets/home/<USER>"),
          key:"gztj"
        },
        {
          name: "草稿箱",
          text: "问题上报草稿",
          path: "/drafts",
          iconAssets: require("@/assets/home/<USER>"),
          key:"cgx"
        },
        {
          name: "专项整治",
          text: "问题专项整治",
          path: "/SpecialRectification",
          iconAssets: require("@/assets/home/<USER>"),
          key:"zxzz"
        },
        {
          name: "待处置任务",
          text: "部门: 12人",
          path: "/PendingTasks",
          iconAssets: require("@/assets/home/<USER>"),
          key:"dczrw"
        },
        {
          name: "牛皮癣",
          text: "牛皮癣整治",
          path: "/smallAd",
          iconAssets: require("@/assets/home/<USER>"),
          key:"npx"
        },
        {
          name: "路段考核",
          text: "路段考核",
          path: "/assessment",
          iconAssets: require("@/assets/home/<USER>"),
          key:"ldkh"
        }
      ],
      currentMenuList: []
    };
  },
  computed: {},
  watch: {},
  created() {
    //添加延迟函数确保vuex: user信息赋值后请求
    setTimeout(() => {
      this.init();
    },500)
  },
  methods: {
    // 下拉刷新
    async onAllRefresh() {
      this.$toast.loading({
        duration: 0, // 持续展示 toast
        forbidClick: true,
        message: "数据加载中..."
      });
      const reFnList = ["getIndexList"].map(fnName => {
        return new Promise((resolve, reject) => {
          this[fnName](resolve, reject);
        });
      });

      let isSuccess = true;
      await Promise.all(reFnList).catch(() => {
        isSuccess = false;
        this.$toast.fail("刷新失败，请重试！");
      });

      if (isSuccess) this.$toast.success("刷新成功");
      this.isLoading = false;
    },
    async init() {
      await this.getInfo()
      await this.getMenuList();
      await this.getIndexList();
      await this.getMenuItemTj();
    },
    getInfo() {
      getInfo(getToken())
          .then((res) => {
            const user = res.user
            const userObj = {
              deptName: user?.dept?.deptName || '',
              deptId: user?.dept?.deptId || '',
              nickName: user.nickName,
              ...user,
              id: res?.dogUserQualifi?.id || '',
              realName: user.userName || '',
              mobile: user.phonenumber || '',
              userQualifi: res?.dogUserQualifi || undefined,
              qualifi: res?.dogQualifi || undefined,
              roleType: res.roleType
            }
            this.vuex("user", userObj);
          })
    },
    //获取菜单列表
    getMenuList() {
      getAPPList({name:"监督指挥app权限"}).then(res => {
        this.currentMenuList = this.AllMenuList.filter(item => {
          return res.data.indexOf(item.key) > -1;
        });
      })
    },
    //获取本日/本月指标
    getIndexList() {
      // 实现获取指标数据的逻辑
      getYdStatistics(this.activeTab).then(res => {
        if (res.code == 200) {
          this.indexList = this.user.roleType == 1?[
            {
              name:res.data.reportCount,
              value:"上报数"
            },
            {
              name:res.data.effectCount,
              value:"有效上报数"
            },
            {
              name:res.data.checkAndVerify,
              value:"核实核查数"
            }
          ]:[
            {
              name:res.data.checkAndVerify,
              value:"处置数"
            },
            {
              name:res.data.effectCount,
              value:"按时处置数"
            },
            {
              name:res.data.reportCount,
              value:"超时处置数"
            },
            {
              name:res.data.otherCount,
              value:"返工数"
            }
          ]
        }
      })
    },
    //部分菜单描述内容统计
    getMenuItemTj() {
      //待办任务统计
      getJdyToDoTask({dealPersonId: this.user.userId}).then(res => {
        let heshiNumber = res.data.filter(item => item.prestatus == 3)[0].aCount
        let hechaNumber = res.data.filter(item => item.prestatus == 16)[0].aCount

        this.currentMenuList.forEach(item => {
          if (item.name == "待办任务") {
            item.text = `核查: ${ hechaNumber } 核实: ${ heshiNumber }`
          }
        })
      })

      //待处置任务统计
      getDeptCount({dealPersonId: this.user.userId}).then(res => {
        this.currentMenuList.forEach(item => {
          if (item.name == "待处置任务") {
            item.text = `部门: ${ res.data }人`
          }
        })
      })
    },
    // 切换标签
    switchTab(tab) {
      this.activeTab = tab;
      this.getIndexList();
    },
    goPersonCenter() {
      this.$router.push('/personCenter');
    }
  }
};
</script>

<style lang="scss" scoped>
.container-head {
  width: 100%;
  height: 228px;
  background-size: cover;
  background: url("~@/assets/home/<USER>") no-repeat center top;
  background-size: 100% auto;
  z-index: 1;
  .head-right {
    position: absolute;
    top: 20px;
    right: 15px;

    .personal-center {
      display: flex;
      align-items: center;
      background-color: rgba(255, 255, 255, 0.2);
      color: white;
      padding: 4px 8px;
      border-radius: 10px;
      font-size: 14px;

      .icon-user {
        margin-right: 2.5px;
        font-size: 14px;
      }

      .icon-arrow {
        margin-left: 2.5px;
        font-size: 12px;
      }
    }
  }
}

.stats-container {
  margin-top: -40px;
  padding: 0 15px;
  z-index: 2;
  .tab-switch {
    background-color: white;
    border-radius: 8px 8px 0 0;
    overflow: hidden;
    box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.05);
    padding-top: 10px;
    ::v-deep .van-tabs {
      .van-tabs__wrap {
        padding: 0;
        transition: all 0.3s ease;

        &::after {
          display: none;
        }
      }

      .van-tab {
        font-size: 18.5px;
        padding: 15px 0;
        transition: all 0.3s ease;

        &--active {
          font-weight: 600;
          transform: scale(1.05);
        }
      }

      .van-tabs__line {
        width: 55px !important;
        left: 0;
        bottom: 15px;
        z-index: 99;
        transition: all 0.3s ease;
      }
    }
  }

  .index-List {
    display: flex;
    background-color: white;
    border-radius: 0 0 8px 8px;
    .index-List-item {
      flex: 1;
      height: 93px;
      display: flex;
      flex-direction: column;
      justify-content: space-evenly;
      align-items: center;
      .index-List-item-number {
        font-family: Roboto, Roboto;
        font-weight: 500;
        font-size: 25px;
        color: #121212;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
      .index-List-item-text {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 14.5px;
        color: #6B7280;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
    }
  }

  .index-base {
    height: 23px;
    background: url("~@/assets/home/<USER>") no-repeat;
    background-size: cover;
    position: relative;
    bottom: 15px;
  }

  .card-report {
    height: 103px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 15px;
    margin-top: 1px;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    background: url("~@/assets/home/<USER>") no-repeat;
    background-size: cover;

    .card-left {
      flex: 1;
      margin-left: 137.5px;
      .card-title {
        font-size: 16.5px;
        font-weight: 500;
        color: #333;
        margin-bottom: 7.5px;
      }

      .card-desc {
        font-size: 12px;
        color: #999;
      }
    }

    .card-right {
      .btn-register {
        background-color: #1989fa;
        color: white;
        padding: 6px 12px;
        border-radius: 16px;
        font-size: 14px;
      }
    }
  }
}

.card-container {
  padding: 15px;
  margin-top: 15px;

  .card-list {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;

    .card-small {
      width: calc(50% - 7.5px);
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      margin-bottom: 15px;

      .card-content {
        display: flex;
        padding: 30px 30px 30px 20px;

        .card-icon {
          width: 45px;
          height: 45px;
          border-radius: 8px;
          margin-right: 10px;
          color: white;
          background-size: 100% 100% !important;
        }

        .card-info {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: center;
          margin-left: 10px;

          .card-title {
            font-size: 15px;
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
          }

          .card-status, .card-desc {
            font-size: 12px;
            color: #999;
          }
        }
      }
    }
  }
}

/* 添加一些动画效果 */
.card-small {
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  }
}

.btn-register {
  transition: background-color 0.3s ease;

  &:active {
    background-color: darken(#1989fa, 10%);
  }
}
</style>
