<!--申请退回-->
<template>
  <div class="container">

    <!-- 导航栏 -->
    <van-nav-bar
        title="申请退回"
        left-arrow
        fixed
        @click-left="$router.go(-1)"
    />

    <!--内容-->
    <div class="remark-area">
      <div class="remark-label-container">
        <div class="remark-label">退回理由</div>
        <idioms-selector @select="selectIdiom">选择惯用语</idioms-selector>
      </div>
      <van-field
          v-model="opinion"
          type="textarea"
          placeholder="请输入退回理由"
          rows="1"
          autosize
      />
    </div>

    <!-- 底部按钮 -->
    <div class="bottom-btns">
      <van-button plain class="cancel-btn" @click="onCancel">取消</van-button>
      <van-button type="info" class="submit-btn" @click="onSubmit">提交</van-button>
    </div>
  </div>
</template>

<script>
import IdiomsSelector from '@/components/IdiomsSelector';
import {addAjcl} from "@/api/common";
export default {
  name: "index",
  components: {
    IdiomsSelector
  },
  data() {
    return {
      opinion:""
    }
  },
  computed: {},
  mounted() {

  },
  methods: {
    // 选择惯用语
    selectIdiom(content) {
      this.opinion = content;
    },
    // 取消按钮
    onCancel() {
      this.$dialog.confirm({
        title: '提示',
        message: '确定要取消申请吗？',
      }).then(() => {
        this.$router.go(-1);
      }).catch(() => {
        // 取消操作
      });
    },

    // 提交按钮
    onSubmit() {
      if (!this.opinion.trim()) {
        this.$toast('请输入退回理由');
        return;
      }

      this.$toast.loading({
        message: '提交中...',
        forbidClick: true,
        duration: 0
      });

      // 提交处理
      addAjcl({opinion: this.opinion,operateType: 13,eventId: this.$route.query.id}).then(res => {
        if (res.code == 200) {
          this.$toast.clear();
          this.$toast.success('提交成功');
          setTimeout(() => {
            this.$router.go(-1);
          }, 1000);
        }
      })
    }
  },
  watch: {}
}
</script>

<style scoped lang="scss">
.remark-area {
  background-color: #fff;
  padding: 15px;
  margin-top: 61px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  .remark-label-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;

    .remark-label {
      font-size: 15px;
      color: #333;
    }
  }
}

// 底部按钮
.bottom-btns {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  padding: 10px 15px;
  background-color: #fff;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);

  .van-button {
    flex: 1;
    height: 40px;
    border-radius: 4px;
  }

  .cancel-btn {
    margin-right: 10px;
  }
}
</style>
