<!--申请延期-->
<template>
  <div class="container">

    <!-- 导航栏 -->
    <van-nav-bar
        title="申请延期"
        left-arrow
        fixed
        @click-left="$router.go(-1)"
    />

    <!--内容-->
    <div class="remark-container">
      <div class="remark-area">
        <div class="remark-label-container">
          <div class="remark-label">剩余延期次数</div>
        </div>
        <van-field
            v-model="NumberOfExtensions"
            readonly
            placeholder=""
            autosize
        />
      </div>
      <div class="remark-area">
        <div class="remark-label-container">
          <div class="remark-label">延期时间</div>
        </div>
        <van-field
            v-model="dealNumber"
            type="number"
            placeholder="请输入延期时间(数字)"
            rows="1"
            autosize
            @input="validateNumber"
        />
      </div>
      <div class="remark-area">
        <div class="remark-label-container">
          <div class="remark-label">时间类型</div>
        </div>
        <van-field
            readonly
            placeholder=""
            autosize
        >
          <template #input>
            <custom-picker
              v-model="dealUnit"
              :columns="dealUnitOptions"
              placeholder="请选择时间类型"
              @confirm="onDealUnitConfirm"
            />
          </template>
        </van-field>
      </div>
      <div class="remark-area">
        <div class="remark-label-container">
          <div class="remark-label">延期理由</div>
          <idioms-selector @select="selectIdiom">选择惯用语</idioms-selector>
        </div>
        <van-field
            v-model="opinion"
            type="textarea"
            placeholder="请输入延期理由"
            rows="1"
            autosize
        />
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="bottom-btns">
      <van-button plain class="cancel-btn" @click="onCancel">取消</van-button>
      <van-button type="info" class="submit-btn" @click="onSubmit">提交</van-button>
    </div>
  </div>
</template>

<script>
import IdiomsSelector from '@/components/IdiomsSelector';
import {addAjcl} from "@/api/common";
import CustomPicker from '@/components/CustomPicker/index.vue';

export default {
  name: "index",
  components: {
    IdiomsSelector,
    CustomPicker
  },
  props: ['id'],
  data() {
    return {
      NumberOfExtensions: "98",
      dealNumber: "",
      dealUnit: "",
      opinion: "",
      dealUnitOptions: [
        {
          text: '时',
          value: 1
        },
        {
          text: '天',
          value: 2
        },
        {
          text: '周',
          value: 3
        },
        {
          text: '月',
          value: 4
        }
      ]
    }
  },
  computed: {},
  mounted() {
    // 初始化默认值
    this.initDefaultValues();
  },
  methods: {
    // 初始化默认值
    initDefaultValues() {
      // 默认选择"天"
      this.dealUnit = 2; // 默认选择"天"
    },
    // 选择惯用语
    selectIdiom(content) {
      this.opinion = content;
    },
    // 取消按钮
    onCancel() {
      this.$dialog.confirm({
        title: '提示',
        message: '确定要取消申请吗？',
      }).then(() => {
        this.$router.go(-1);
      }).catch(() => {
        // 取消操作
      });
    },

    validateNumber() {
      // 确保输入的是正整数
      const num = this.dealNumber.toString().trim();
      if (num) {
        // 移除非数字字符
        const cleanNum = num.replace(/[^\d]/g, '');
        // 如果有非数字字符被移除，或者数字是0开头但不是0本身
        if (cleanNum !== num || (cleanNum.length > 1 && cleanNum.startsWith('0'))) {
          // 更新为有效的正整数
          this.dealNumber = cleanNum.replace(/^0+/, '') || '0';
        }
      }
    },

    // 时间类型选择确认
    onDealUnitConfirm(value) {
      console.log('选择的时间类型：', value);
      // CustomPicker组件已经自动更新了dealUnit的值，此处可以做额外处理
    },

    // 提交按钮
    onSubmit() {
      if (!this.dealNumber || parseInt(this.dealNumber) <= 0) {
        this.$toast('请输入有效的延期时间');
        return;
      }

      if (!this.dealUnit) {
        this.$toast('请选择时间类型');
        return;
      }

      if (!this.opinion.trim()) {
        this.$toast('请输入延期理由');
        return;
      }

      this.$toast.loading({
        message: '提交中...',
        forbidClick: true,
        duration: 0
      });

      // 提交处理
      addAjcl({
        opinion: this.opinion,
        operateType: 12,
        eventId: this.$route.query.id,
        dealNumber: parseInt(this.dealNumber),
        dealUnit: parseInt(this.dealUnit)
      }).then(res => {
        if (res.code == 200) {
          this.$toast.clear();
          this.$toast.success('提交成功');
          setTimeout(() => {
            this.$router.go(-1);
          }, 1000);
        }
      }).catch(err => {
        this.$toast.clear();
        this.$toast.fail('提交失败');
        console.error('提交失败:', err);
      });
    }
  },
  watch: {}
}
</script>

<style scoped lang="scss">
.remark-container {
  margin-top: 61px;
  .remark-area {
    background-color: #fff;
    padding: 15px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

    .remark-label-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;

      .remark-label {
        font-size: 15px;
        color: #333;
      }
    }
  }
}


// 底部按钮
.bottom-btns {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  padding: 10px 15px;
  background-color: #fff;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);

  .van-button {
    flex: 1;
    height: 40px;
    border-radius: 4px;
  }

  .cancel-btn {
    margin-right: 10px;
  }
}
</style>
