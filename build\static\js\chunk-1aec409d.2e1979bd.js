(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1aec409d"],{"21a5":function(t,e,a){},"31ac":function(t,e,a){},3224:function(t,e,a){"use strict";a("f833")},4558:function(t,e,a){},5945:function(t,e,a){"use strict";var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"map-container"},[e("div",{ref:"mapContainer",attrs:{id:"map"}}),t.currentAddress?e("div",{staticClass:"address-info"},[e("p",[t._v("经纬度: "+t._s(t.currentPosition.lat)+", "+t._s(t.currentPosition.lng))]),e("p",[t._v("地址: "+t._s(t.currentAddress))])]):t._e(),e("div",{staticStyle:{position:"absolute",bottom:"5px",left:"178px","z-index":"9999",display:"flex",gap:"10px"}},[t.type?e("van-button",{attrs:{disabled:t.isCanPoint,size:"small",type:"primary"},on:{click:t.flag}},[t._v("标记位置")]):t._e()],1)])},o=[],s=(a("e9f5"),a("ab43"),{name:"MapComponent",props:{coordinates:{type:[Array,Object],default:null}},data(){return{type:1,editMode:!1,map:null,marker:null,locationCircle:null,currentPosition:{lat:null,lng:null},isCanPoint:!1,currentAddress:"",tiandituKey:"301de34e264e4a45b3d000d5cff0a870"}},mounted(){this.initMap()},methods:{initMap(){if(this.map=L.map("map",{center:[29.110764,119.630857],zoom:14,zoomControl:!0,attributionControl:!1}),L.tileLayer("https://t0.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk="+this.tiandituKey,{maxZoom:18,tileSize:256,zoomOffset:0}).addTo(this.map),L.tileLayer("https://t0.tianditu.gov.cn/cia_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk="+this.tiandituKey,{maxZoom:18,tileSize:256,zoomOffset:0}).addTo(this.map),this.map.on("click",this.handleMapClick),this.coordinates){console.log("this.coordinates",this.coordinates),this.type=0;const t=Array.isArray(this.coordinates)?this.coordinates[0]:this.coordinates.lng,e=Array.isArray(this.coordinates)?this.coordinates[1]:this.coordinates.lat;this.currentPosition={lat:e,lng:t},this.reverseGeocode(e,t),this.addInitialMarker(e,t)}},handleMapClick(t){if(this.isCanPoint||this.editMode){const{lat:e,lng:a}=t.latlng;this.currentPosition={lat:e,lng:a},this.marker&&(this.map.removeLayer(this.marker),this.marker=null),this.marker=L.marker([e,a]).addTo(this.map),this.reverseGeocode(e,a)}this.isCanPoint=!1,this.editMode=!1},async reverseGeocode(t,e){try{const a=await fetch(`https://api.tianditu.gov.cn/geocoder?postStr={'lon':${e},'lat':${t},'ver':1}&type=geocode&tk=${this.tiandituKey}`),i=await a.json();"0"===i.status?(console.log(i),this.currentAddress=i.result.formatted_address,this.$emit("locationSelected",i.result)):this.currentAddress="获取地址失败"}catch(a){console.error("逆地理编码失败:",a),this.currentAddress="获取地址失败"}},addInitialMarker(t,e){let a=this;a.marker&&a.map.removeLayer(a.marker),a.marker=L.marker([t,e]).addTo(a.map),a.map.setView([t,e],12)},flag(){this.isCanPoint=!0},bianji(){this.editMode=!0}}}),n=s,r=(a("7b83"),a("2877")),l=Object(r["a"])(n,i,o,!1,null,"673de8dc",null);e["a"]=l.exports},"7b83":function(t,e,a){"use strict";a("31ac")},8080:function(t,e,a){"use strict";var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"custom-picker"},[e("div",{staticClass:"picker-trigger",on:{click:t.showPicker}},[t._t("trigger",(function(){return[e("div",{staticClass:"selected-value",class:{valueColor:t.showValue}},[t._v(" "+t._s(t.showValue||t.placeholder)+" ")]),e("van-icon",{attrs:{name:"arrow"}})]}))],2),e("van-popup",{attrs:{round:"",position:"bottom"},model:{value:t.isVisible,callback:function(e){t.isVisible=e},expression:"isVisible"}},[e("van-picker",{attrs:{columns:t.columns,"default-index":t.defaultIndex,"show-toolbar":"",title:t.title,loading:t.loading},on:{confirm:t.onConfirm,cancel:t.onCancel,change:t.onChange}})],1)],1)},o=[],s=(a("e9f5"),a("f665"),{name:"CustomPicker",props:{columns:{type:Array,default:()=>[]},value:{type:[String,Number],default:""},title:{type:String,default:"请选择"},placeholder:{type:String,default:"请选择"},loading:{type:Boolean,default:!1}},data(){return{isVisible:!1,defaultIndex:0}},computed:{showValue(){const t=this.columns.find(t=>t===this.value||"object"===typeof t&&t.value===this.value);return t?"object"===typeof t?t.text:t:""}},watch:{value:{handler(t){this.setDefaultIndex(t)},immediate:!0}},methods:{setDefaultIndex(t){try{this.defaultIndex=this.columns.findIndex(e=>e===t||"object"===typeof e&&e.value===t),-1===this.defaultIndex&&(this.defaultIndex=0)}catch(e){console.error("设置默认值失败:",e),this.defaultIndex=0}},showPicker(){this.isVisible=!0},onConfirm(t,e){this.isVisible=!1;const a="object"===typeof t?t.value:t;this.$emit("input",a),this.$emit("confirm",t,e)},onCancel(){this.isVisible=!1,this.$emit("cancel")},onChange(t,e,a){this.$emit("change",{picker:t,value:e,index:a})}}}),n=s,r=(a("c3d6"),a("2877")),l=Object(r["a"])(n,i,o,!1,null,"713dcdce",null);e["a"]=l.exports},b755:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"problem-report"},[e("van-nav-bar",{attrs:{title:"牛皮癣","left-arrow":""},on:{"click-left":t.onClickLeft},scopedSlots:t._u([{key:"right",fn:function(){return[e("div",{staticClass:"nav-right"},[e("span",{staticClass:"save-draft"},[t._v("存草稿")]),e("van-icon",{staticClass:"search-icon",attrs:{name:"search",size:"18"}})],1)]},proxy:!0}])}),e("div",{staticClass:"form-content"},[e("van-cell-group",{staticClass:"form-group"},[e("van-field",{attrs:{size:"large",label:"类型",readonly:""},scopedSlots:t._u([{key:"input",fn:function(){return[t._v("事件")]},proxy:!0}])}),e("van-field",{attrs:{size:"large",label:"大类",readonly:""},scopedSlots:t._u([{key:"input",fn:function(){return[t._v("宣传广告")]},proxy:!0}])}),e("van-field",{attrs:{size:"large",label:"小类",readonly:""},scopedSlots:t._u([{key:"input",fn:function(){return[t._v("非法小广告")]},proxy:!0}])}),e("van-field",{attrs:{size:"large",label:"立案标准",readonly:""},scopedSlots:t._u([{key:"input",fn:function(){return[t._v("公共场所内、公告设施上非法张贴、喷涂、手写的各类广告及乱涂乱画现象")]},proxy:!0}])})],1),e("van-cell-group",{staticClass:"form-group"},[e("van-field",{attrs:{size:"large",label:"位置选择",readonly:"",placeholder:"","right-icon":"arrow"},scopedSlots:t._u([{key:"right-icon",fn:function(){return[e("div",{staticClass:"section-content"},[e("van-button",{staticClass:"action-btn",attrs:{plain:"",type:"primary",size:"small"},on:{click:function(e){t.showMapDialog=!0}}},[t._v(" 选择定位 ")])],1)]},proxy:!0}])}),e("van-field",{attrs:{size:"large",label:"位置描述",readonly:"",placeholder:"","right-icon":"arrow"},scopedSlots:t._u([{key:"right-icon",fn:function(){return[e("div",{staticClass:"section-content"},[e("van-button",{staticClass:"action-btn",attrs:{plain:"",type:"primary",size:"small"}},[t._v("自动生成")])],1)]},proxy:!0}])}),e("van-field",{attrs:{type:"textarea",label:"",placeholder:"请输入位置描述",rows:"2",autosize:"",maxlength:"50","show-word-limit":""},model:{value:t.formData.address,callback:function(e){t.$set(t.formData,"address",e)},expression:"formData.address"}}),e("van-field",{attrs:{size:"large",label:"问题描述",readonly:"",placeholder:"","right-icon":"arrow"},scopedSlots:t._u([{key:"right-icon",fn:function(){return[e("div",{staticClass:"section-content"},[e("van-button",{staticClass:"action-btn",attrs:{plain:"",type:"primary",size:"small"}},[t._v("选择惯用语")])],1)]},proxy:!0}])}),e("van-field",{attrs:{type:"textarea",label:"",placeholder:"请输入问题描述",rows:"2",autosize:"",maxlength:"50","show-word-limit":""},model:{value:t.formData.eventdesc,callback:function(e){t.$set(t.formData,"eventdesc",e)},expression:"formData.eventdesc"}}),e("van-field",{attrs:{type:"textarea",label:"手机号码",placeholder:"请输入手机号码",rows:"2",autosize:"",maxlength:"50","show-word-limit":""},model:{value:t.formData.reporterphone,callback:function(e){t.$set(t.formData,"reporterphone",e)},expression:"formData.reporterphone"}}),e("div",{staticClass:"form-section"},[e("div",{staticClass:"section-label"},[t._v("图片选择（最少一张）")])]),e("div",{staticClass:"uploader-wrapper"},[e("vantFileUpload",{attrs:{"file-type":["jpg","png"]},on:{change:t.handleFileChange},model:{value:t.formData.fileStr,callback:function(e){t.$set(t.formData,"fileStr",e)},expression:"formData.fileStr"}})],1),e("van-field",{attrs:{size:"large",label:"是否自办结",readonly:"",placeholder:"","right-icon":"arrow"},scopedSlots:t._u([{key:"right-icon",fn:function(){return[e("van-radio-group",{attrs:{direction:"horizontal"},model:{value:t.formData.isSelfComplete,callback:function(e){t.$set(t.formData,"isSelfComplete",e)},expression:"formData.isSelfComplete"}},[e("van-radio",{attrs:{name:"1"}},[t._v("是")]),e("van-radio",{attrs:{name:"0"}},[t._v("否")])],1)]},proxy:!0}])})],1)],1),e("div",{staticClass:"submit-btn-wrapper"},[e("van-button",{attrs:{type:"info",block:""},on:{click:t.onSubmit}},[t._v("提交")])],1),e("van-popup",{style:{height:"80%",width:"100%"},attrs:{position:"bottom"},model:{value:t.showMapDialog,callback:function(e){t.showMapDialog=e},expression:"showMapDialog"}},[e("div",{staticClass:"map-dialog"},[e("Map",{ref:"mapComponent",on:{locationSelected:t.handleLocationSelected}}),e("div",{staticClass:"map-dialog-footer"},[e("van-button",{attrs:{type:"info",block:""},on:{click:t.confirmLocation}},[t._v("确认位置")])],1)],1)])],1)},o=[],s=a("8080"),n=a("eeb8"),r=a("5945"),l=a("4020");function c(t){return Object(l["a"])({url:"/zhcg/npx/add",method:"post",data:t})}var d={name:"ProblemReport",components:{CustomPicker:s["a"],TabSwitch:n["a"],Map:r["a"]},data(){return{showMapDialog:!1,formData:{source:"",createid:"",type1id:"npx_wtlx_sj",type2id:"zhcg_npx_dl_xcgg",type3id:"zhcg_npx_dl_ffxgg",type4id:"zhcg_npx_ldbz",areaid:"",streetid:"",address:"",eventdesc:"",eventType:2,images:[],isSelfComplete:"0",reporterphone:"",fileStr:""},columns:{type1Options:[{text:"类型1",value:"1"},{text:"类型2",value:"2"},{text:"类型3",value:"3"}],type2Options:[{text:"大类1",value:"1"},{text:"大类2",value:"2"},{text:"大类3",value:"3"}],type3Options:[{text:"小类1",value:"1"},{text:"小类2",value:"2"},{text:"小类3",value:"3"}],type4Options:[{text:"标准1",value:"1"},{text:"标准2",value:"2"},{text:"标准3",value:"3"}]}}},mounted(){this.initParams()},methods:{initParams(){this.formData.source="1",this.formData.createid=this.user.info.nickName},onClickLeft(){this.$router.go(-1)},handleLocationSelected({addressComponent:t,formatted_address:e,location:a}){console.log("location",a),this.formData.address=e,this.formData.cityid=t.city_code.slice(3),this.formData.cityName=t.city,this.formData.areaid=t.county_code.slice(3),this.formData.areaName=t.county,this.formData.streetid=t.town_code.slice(3),this.formData.streetName=t.town,this.formData.y84=a.lat,this.formData.x84=a.lon},confirmLocation(){this.showMapDialog=!1},onPickerConfirm(t,e){console.log(t+" selected:",e)},handleFileChange(t){console.log("文件已更新:",t),t&&this.savingDraft&&console.log("文件变化，可以考虑自动更新草稿")},afterRead(t){console.log(t)},onSubmit(){return this.formData.type1id?this.formData.type2id?this.formData.type3id?this.formData.type4id?this.formData.address?this.formData.eventdesc?0===this.formData.fileStr.length?this.$toast("请至少上传一张图片"):(this.$toast.loading({message:"提交中...",forbidClick:!0}),void c(this.formData).then(t=>{200===t.code?(this.$toast.success("提交成功"),this.$router.go(-1)):this.$toast.fail("提交失败")})):this.$toast("请输入问题描述"):this.$toast("请输入位置描述"):this.$toast("请选择立案标准"):this.$toast("请选择小类"):this.$toast("请选择大类"):this.$toast("请选择类型")}}},u=d,p=(a("dd48"),a("2877")),h=Object(p["a"])(u,i,o,!1,null,"73783dbe",null);e["default"]=h.exports},c3d6:function(t,e,a){"use strict";a("21a5")},dd48:function(t,e,a){"use strict";a("4558")},eeb8:function(t,e,a){"use strict";var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"tab-switch"},[e("van-tabs",{attrs:{border:!1,"line-width":20,"line-height":"2px",color:"#1989fa","title-active-color":"#1989fa","title-inactive-color":"#666"},on:{change:t.handleChange},model:{value:t.modelValue,callback:function(e){t.modelValue=e},expression:"modelValue"}},t._l(t.tabs,(function(t,a){return e("van-tab",{key:a,attrs:{title:t.label,name:t.value}})})),1)],1)},o=[],s={name:"TabSwitch",props:{tabs:{type:Array,default:()=>[]},value:{type:[String,Number],default:""}},computed:{modelValue:{get(){return this.value},set(t){this.$emit("input",t)}}},methods:{handleChange(t){this.$emit("change",t)}}},n=s,r=(a("3224"),a("2877")),l=Object(r["a"])(n,i,o,!1,null,"185e5f8f",null);e["a"]=l.exports},f833:function(t,e,a){}}]);