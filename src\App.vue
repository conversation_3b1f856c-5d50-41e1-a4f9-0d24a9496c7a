<template>
  <div id="app">
      <router-view />
  </div>
</template>

<script>
import setRem from '@/util/rem'
export default {
  name: 'App',
  components: {

  },
  data() {
    return {
      params: ''
    }
  },
  mounted() {
    setRem()
    // 改变窗口大小时重新设置 rem
    window.onresize = () => {
      setRem()
    }
  }
}
</script>

<style>
#app {
  min-height: 100vh;
  margin: 0;
  font-family: Source <PERSON> Sans CN-Regular, Source <PERSON> CN-Medium, Source <PERSON> CN-Bold,
  Source <PERSON>, sans-serif;
  color: #333;
  background: #fff;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</style>
