<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script>
import setRem from "@/util/rem";
export default {
  name: "App",
  components: {},
  data() {
    return {
      params: "",
    };
  },
  watch: {
    "$route.path": {
      handler(toPath, fromPath) {
        console.log("当前页面路由地址：" + toPath);
        console.log("上一个路由地址：" + fromPath);
        this.watchRouter();
      },
    },
  },
  mounted() {
    this.addListener();
    setRem();
    // 改变窗口大小时重新设置 rem
    window.onresize = () => {
      setRem();
    };
  },
  methods: {
    watchRouter() {
      setTimeout(() => {
        console.log("路由跳转");
        let canExitType = "";
        // if (this.$route.path == '/' || this.$route.path == '/login') {
        // canExitType = "canExit";
        //   console.log('canExit')
        // } else {
          canExitType = 'canNotExit'
        //   console.log('canNotExit')
        // }
        window.parent.postMessage(
          {
            cmd: "route_change",
            currentPage: this.$route.path,
            canExitType: canExitType,
          },
          "*"
        );
      }, 600); // 延迟去获得跳转后的页面路由
    },
    addListener() {
      window.addEventListener("message", (event) => {
        if (event.data.cmd == "navi_back") {
          console.log("frontstation-route_path", this.$route.path);

          if (this.$route.path == "/") {
            window.location.href = "/ygfMobileJc";
          } else {
            history.back();
          }
        }
      });
    },
  },
};
</script>

<style>
#app {
  min-height: 100vh;
  margin: 0;
  font-family: Source Han Sans CN-Regular, Source Han Sans CN-Medium,
    Source Han Sans CN-Bold, Source Han Sans CN, sans-serif;
  color: #333;
  background: #fff;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</style>
