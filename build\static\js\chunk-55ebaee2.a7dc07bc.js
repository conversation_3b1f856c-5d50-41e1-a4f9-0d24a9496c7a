(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-55ebaee2"],{1766:function(t,e,i){},"207e":function(t,e,i){"use strict";var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"vant-upload-file"},[e("van-uploader",{ref:"upload",attrs:{"max-count":t.limit,accept:t.accept,disabled:t.$attrs.disabled,"max-size":1024*t.fileSize*1024,"before-read":t.handleBeforeUpload,"after-read":t.handleAfterRead,"upload-text":t.uploadText,"upload-icon":t.uploadIcon,capture:"camera"},scopedSlots:t._u([{key:"default",fn:function(){return[t._t("default",(function(){return[e("div",{staticClass:"upload-icon-wrapper"},[e("van-icon",{staticClass:"upload-icon",attrs:{name:"photograph"}})],1)]}))]},proxy:!0}],null,!0),model:{value:t.internalFileList,callback:function(e){t.internalFileList=e},expression:"internalFileList"}}),t.showFileList&&t.fileList.length>0?e("div",{staticClass:"file-list-container"},[e("div",{staticClass:"file-list-title"},[t._v("已上传文件")]),t.$attrs.disabled?[e("div",{staticClass:"file-list-wrapper"},[t._l(t.fileList,(function(i,a){return e("div",{key:i.uid||a,staticClass:"file-item disabled"},[e("div",{staticClass:"file-info",on:{click:function(e){return t.handlePreview(i)}}},[e("van-icon",{staticClass:"file-icon",attrs:{name:"description"}}),e("div",{staticClass:"file-name van-ellipsis"},[t._v(t._s(i.name))])],1)])})),t.fileList.length>2?e("van-button",{staticClass:"toggle-button",attrs:{size:"mini",type:"default"},on:{click:t.toggleExpand}},[t._v(" "+t._s(t.isExpanded?"收起":"展开")+" ")]):t._e()],2)]:t._l(t.fileList,(function(i,a){return e("div",{key:i.uid||a,staticClass:"file-item"},[e("div",{staticClass:"file-info",on:{click:function(e){return t.handlePreview(i)}}},[e("van-icon",{staticClass:"file-icon",attrs:{name:"description"}}),e("div",{staticClass:"file-name van-ellipsis"},[t._v(t._s(i.name))])],1),e("div",{staticClass:"file-actions"},[e("van-icon",{staticClass:"delete-icon",attrs:{name:"delete"},on:{click:function(e){return t.handleFileDelete(a)}}})],1)])}))],2):t._e(),t.showTip?e("div",{staticClass:"upload-tip"},[e("p",[e("span",[t._v("支持格式："+t._s(t.fileTypeText))]),t.fileSize?e("span",[t._v("，大小不超过 "+t._s(t.fileSize)+"MB")]):t._e()])]):t._e()],1)},s=[],l=(i("e9f5"),i("910d"),i("7d54"),i("ab43"),i("a732"),i("0a5a")),o=i("4260"),n={name:"index",props:{limit:{type:Number,default:4},accept:{type:String,default:"image/*"},action:{type:String,default:"/prod-api/sysUploadFile/uploadFile"},autoUpload:{type:Boolean,default:!0},fileList:{type:Array,default:()=>[]},data:{type:Object,default:()=>({})},name:{type:String,default:"multipartFile"},value:[String,Object,Array,Number],fileSize:{type:Number,default:20},fileType:{type:Array,default:()=>["doc","docx","pdf","txt","xls","xlsx","png","jpg","jpeg","gif","mp3","mp4","mov","avi"]},isShowTip:{type:Boolean,default:!0},showFileList:{type:Boolean,default:!0},uploadIcon:{type:String,default:"photograph"},uploadText:{type:String,default:"上传文件"}},data(){return{headers:{Authorization:"Bearer "+Object(l["a"])()},internalFileList:[],isExpanded:!1,uploading:!1}},computed:{showTip(){return!this.$attrs.disabled&&(this.isShowTip&&(this.fileType||this.fileSize))},fileTypeText(){return this.fileType.join("、")}},watch:{fileList:{handler(t){this.internalFileList=t.map(t=>({url:Object(o["c"])(t),name:this.getFileName(t),isImage:/\.(jpeg|jpg|gif|png|webp)$/i.test(t||"")}))},immediate:!0},value:{handler(t){t?(this.fetchFiles(t),this.$emit("change",t)):(this.internalFileList=[],this.$emit("change",""))},immediate:!0},internalFileList:{handler(t){if(t&&t.length>0){const e=t.map(t=>t.uid||"").filter(Boolean).join(",");e&&e!==this.value&&(this.$emit("input",e),this.$emit("change",e))}else this.value&&(this.$emit("input",""),this.$emit("change",""))},deep:!0}},created(){this.value&&!this.internalFileList.length&&this.fetchFiles(this.value)},methods:{fetchFiles(t){if(t){const e=t.split(",").filter(Boolean).map(t=>({name:this.getFileName(t),url:Object(o["c"])(t),uid:t,isImage:/\.(jpeg|jpg|gif|png|webp)$/i.test(t||"")}));this.$emit("update:fileList",e),this.internalFileList=e}},handleBeforeUpload(t){if(this.fileType&&this.fileType.length>0){let e="";t.name.lastIndexOf(".")>-1&&(e=t.name.slice(t.name.lastIndexOf(".")+1).toLowerCase());const i=this.fileType.some(i=>t.type.indexOf(i)>-1||!(!e||i.toLowerCase()!==e));if(!i)return this.$toast.fail(`文件格式不正确, 请上传${this.fileType.join("/")}格式文件!`),!1}if(this.fileSize){const e=t.size/1024/1024<this.fileSize;if(!e)return this.$toast.fail(`上传文件大小不能超过 ${this.fileSize}MB!`),!1}return!0},handleAfterRead(t){this.autoUpload?this.uploadFile(t):this.$emit("select",t)},uploadFile(t){this.uploading=!0;const e=new FormData;t.file&&e.append(this.name,t.file),this.data&&Object.keys(this.data).forEach(t=>{e.append(t,this.data[t])}),t.status="uploading",t.message="上传中...",fetch(this.action,{method:"POST",headers:this.headers,body:e}).then(t=>t.json()).then(e=>{if(this.uploading=!1,200===e.code){if(t.status="done",t.message="上传成功",e.data){Object.assign(t,{url:Object(o["c"])(e.data),uid:e.data});const i=this.internalFileList.map(t=>t.uid||"").filter(Boolean).join(",");this.$emit("input",i),this.$emit("change",i)}this.$emit("success",{response:e,file:t,fileList:this.internalFileList})}else t.status="failed",t.message=e.msg||"上传失败",this.$toast.fail("文件上传失败，请重新上传"),this.$emit("error","文件上传失败，请重新上传")}).catch(e=>{this.uploading=!1,t.status="failed",t.message="上传失败",this.$toast.fail("文件上传失败，请重新上传"),this.$emit("error",e.message||"文件上传失败，请重新上传")})},handleFileDelete(t){this.$dialog.confirm({title:"提示",message:"是否确认删除？删除后将无法恢复"}).then(()=>{const e=this.internalFileList[t];this.internalFileList.splice(t,1),this.$emit("remove",e),this.$emit("update:fileList",[...this.internalFileList]),this.$emit("input",this.internalFileList.map(t=>t.uid).join(","))}).catch(()=>{})},handlePreview(t){if(this.$emit("preview",t),t.isImage){const e=this.internalFileList.filter(t=>t.isImage).map(t=>t.url),i=e.indexOf(t.url);this.$imagePreview({images:e,startPosition:i>-1?i:0,closeable:!0})}else t.url&&window.open(t.url)},toggleExpand(){this.isExpanded=!this.isExpanded},getFileName(t){return t?t.lastIndexOf("/")>-1?t.slice(t.lastIndexOf("/")+1).toLowerCase():t:""},submit(){if(this.internalFileList.some(t=>"uploading"===t.status))return void this.$toast("有文件正在上传中，请稍后再试");const t=this.internalFileList.filter(t=>!t.status||"failed"===t.status);0!==t.length?t.forEach(t=>{this.uploadFile(t)}):this.$toast("没有需要上传的文件")},clearFiles(){this.internalFileList=[],this.$emit("update:fileList",[]),this.$emit("input","")}}},r=n,d=(i("58d9"),i("2877")),c=Object(d["a"])(r,a,s,!1,null,"6abab3f2",null);e["a"]=c.exports},"21a5":function(t,e,i){},"31ac":function(t,e,i){},3224:function(t,e,i){"use strict";i("f833")},"3a67":function(t,e,i){"use strict";i("519f")},"519f":function(t,e,i){},"58d9":function(t,e,i){"use strict";i("629c")},5945:function(t,e,i){"use strict";var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"map-container"},[e("div",{ref:"mapContainer",attrs:{id:"map"}}),t.currentAddress?e("div",{staticClass:"address-info"},[e("p",[t._v("经纬度: "+t._s(t.currentPosition.lat)+", "+t._s(t.currentPosition.lng))]),e("p",[t._v("地址: "+t._s(t.currentAddress))])]):t._e(),e("div",{staticStyle:{position:"absolute",bottom:"5px",left:"178px","z-index":"9999",display:"flex",gap:"10px"}},[t.type?e("van-button",{attrs:{disabled:t.isCanPoint,size:"small",type:"primary"},on:{click:t.flag}},[t._v("标记位置")]):t._e()],1)])},s=[],l=(i("e9f5"),i("ab43"),{name:"MapComponent",props:{coordinates:{type:[Array,Object],default:null}},data(){return{type:1,editMode:!1,map:null,marker:null,locationCircle:null,currentPosition:{lat:null,lng:null},isCanPoint:!1,currentAddress:"",tiandituKey:"301de34e264e4a45b3d000d5cff0a870"}},mounted(){this.initMap()},methods:{initMap(){if(this.map=L.map("map",{center:[29.110764,119.630857],zoom:14,zoomControl:!0,attributionControl:!1}),L.tileLayer("https://t0.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk="+this.tiandituKey,{maxZoom:18,tileSize:256,zoomOffset:0}).addTo(this.map),L.tileLayer("https://t0.tianditu.gov.cn/cia_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk="+this.tiandituKey,{maxZoom:18,tileSize:256,zoomOffset:0}).addTo(this.map),this.map.on("click",this.handleMapClick),this.coordinates){console.log("this.coordinates",this.coordinates),this.type=0;const t=Array.isArray(this.coordinates)?this.coordinates[0]:this.coordinates.lng,e=Array.isArray(this.coordinates)?this.coordinates[1]:this.coordinates.lat;this.currentPosition={lat:e,lng:t},this.reverseGeocode(e,t),this.addInitialMarker(e,t)}},handleMapClick(t){if(this.isCanPoint||this.editMode){const{lat:e,lng:i}=t.latlng;this.currentPosition={lat:e,lng:i},this.marker&&(this.map.removeLayer(this.marker),this.marker=null),this.marker=L.marker([e,i]).addTo(this.map),this.reverseGeocode(e,i)}this.isCanPoint=!1,this.editMode=!1},async reverseGeocode(t,e){try{const i=await fetch(`https://api.tianditu.gov.cn/geocoder?postStr={'lon':${e},'lat':${t},'ver':1}&type=geocode&tk=${this.tiandituKey}`),a=await i.json();"0"===a.status?(console.log(a),this.currentAddress=a.result.formatted_address,this.$emit("locationSelected",a.result)):this.currentAddress="获取地址失败"}catch(i){console.error("逆地理编码失败:",i),this.currentAddress="获取地址失败"}},addInitialMarker(t,e){let i=this;i.marker&&i.map.removeLayer(i.marker),i.marker=L.marker([t,e]).addTo(i.map),i.map.setView([t,e],12)},flag(){this.isCanPoint=!0},bianji(){this.editMode=!0}}}),o=l,n=(i("7b83"),i("2877")),r=Object(n["a"])(o,a,s,!1,null,"673de8dc",null);e["a"]=r.exports},"629c":function(t,e,i){},"6e29":function(t,e,i){"use strict";var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"idioms-selector"},[e("van-button",{staticClass:"action-btn",class:{"is-plain":t.plain},attrs:{type:t.type,plain:t.plain,disabled:t.disabled},on:{click:t.showPopup}},[t._t("default",(function(){return[t._v(t._s(t.buttonText))]}))],2),e("van-popup",{staticClass:"idioms-popup",style:{maxHeight:"70%"},attrs:{round:"",position:"bottom"},model:{value:t.visible,callback:function(e){t.visible=e},expression:"visible"}},[e("div",{staticClass:"popup-header"},[e("div",{staticClass:"popup-title"},[t._v("选择惯用语")]),e("van-icon",{staticClass:"close-icon",attrs:{name:"cross"},on:{click:t.closePopup}})],1),e("div",{staticClass:"popup-content"},[t.loading?e("div",{staticClass:"loading-container"},[e("van-loading",{attrs:{type:"spinner",color:"#1989fa"}}),e("span",{staticClass:"loading-text"},[t._v("加载中...")])],1):0===t.idiomsList.length?e("div",{staticClass:"empty-container"},[e("van-empty",{attrs:{description:"暂无惯用语"}}),e("div",{staticClass:"empty-action"},[e("van-button",{attrs:{plain:"",type:"info",size:"small"},on:{click:t.goToSettings}},[t._v(" 去添加 ")])],1)],1):e("div",{staticClass:"idioms-list"},t._l(t.idiomsList,(function(i,a){return e("div",{key:a,staticClass:"idiom-item",on:{click:function(e){return t.selectIdiom(i.phrase)}}},[e("div",{staticClass:"idiom-content"},[t._v(t._s(i.phrase))])])})),0)]),e("div",{staticClass:"popup-footer"},[e("van-button",{attrs:{block:"",type:"info"},on:{click:t.closePopup}},[t._v("取消")])],1)])],1)},s=[],l=(i("14d9"),i("2934")),o={name:"IdiomsSelector",props:{buttonText:{type:String,default:"选择惯用语"},type:{type:String,default:"info"},plain:{type:Boolean,default:!0},target:{type:String,default:""},disabled:{type:Boolean,default:!1}},data(){return{visible:!1,idiomsList:[],loading:!1,queryParams:{pageNum:1,pageSize:50}}},methods:{showPopup(){this.disabled||(this.visible=!0,this.getIdiomsList())},closePopup(){this.visible=!1},async getIdiomsList(){try{this.loading=!0;const t=await Object(l["j"])(this.queryParams);200===t.code?this.idiomsList=t.rows||[]:this.$toast.fail(t.msg||"获取惯用语失败")}catch(t){console.error("获取惯用语列表失败:",t),this.$toast.fail("获取惯用语失败")}finally{this.loading=!1}},selectIdiom(t){this.$emit("select",t),this.closePopup()},goToSettings(){this.$router.push("/IdiomaticExpressions"),this.closePopup()}}},n=o,r=(i("7d90"),i("2877")),d=Object(r["a"])(n,a,s,!1,null,"24e0cf40",null);e["a"]=d.exports},"7b83":function(t,e,i){"use strict";i("31ac")},"7d90":function(t,e,i){"use strict";i("1766")},8080:function(t,e,i){"use strict";var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"custom-picker"},[e("div",{staticClass:"picker-trigger",on:{click:t.showPicker}},[t._t("trigger",(function(){return[e("div",{staticClass:"selected-value",class:{valueColor:t.showValue}},[t._v(" "+t._s(t.showValue||t.placeholder)+" ")]),e("van-icon",{attrs:{name:"arrow"}})]}))],2),e("van-popup",{attrs:{round:"",position:"bottom"},model:{value:t.isVisible,callback:function(e){t.isVisible=e},expression:"isVisible"}},[e("van-picker",{attrs:{columns:t.columns,"default-index":t.defaultIndex,"show-toolbar":"",title:t.title,loading:t.loading},on:{confirm:t.onConfirm,cancel:t.onCancel,change:t.onChange}})],1)],1)},s=[],l=(i("e9f5"),i("f665"),{name:"CustomPicker",props:{columns:{type:Array,default:()=>[]},value:{type:[String,Number],default:""},title:{type:String,default:"请选择"},placeholder:{type:String,default:"请选择"},loading:{type:Boolean,default:!1}},data(){return{isVisible:!1,defaultIndex:0}},computed:{showValue(){const t=this.columns.find(t=>t===this.value||"object"===typeof t&&t.value===this.value);return t?"object"===typeof t?t.text:t:""}},watch:{value:{handler(t){this.setDefaultIndex(t)},immediate:!0}},methods:{setDefaultIndex(t){try{this.defaultIndex=this.columns.findIndex(e=>e===t||"object"===typeof e&&e.value===t),-1===this.defaultIndex&&(this.defaultIndex=0)}catch(e){console.error("设置默认值失败:",e),this.defaultIndex=0}},showPicker(){this.isVisible=!0},onConfirm(t,e){this.isVisible=!1;const i="object"===typeof t?t.value:t;this.$emit("input",i),this.$emit("confirm",t,e)},onCancel(){this.isVisible=!1,this.$emit("cancel")},onChange(t,e,i){this.$emit("change",{picker:t,value:e,index:i})}}}),o=l,n=(i("c3d6"),i("2877")),r=Object(n["a"])(o,a,s,!1,null,"713dcdce",null);e["a"]=r.exports},"81a1":function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"problem-report"},[e("van-nav-bar",{attrs:{title:t.isDetail?"整治详情":"专项整治","left-arrow":""},on:{"click-left":t.onClickLeft}}),e("div",{staticClass:"form-content"},[e("van-cell-group",{staticClass:"form-group"},[e("van-field",{attrs:{size:"large",label:"类型",readonly:""},scopedSlots:t._u([{key:"input",fn:function(){return[e("custom-picker",{attrs:{columns:t.columns.type1Options,placeholder:"请选择",disabled:t.isDetail},on:{confirm:function(e){return t.handleType1Change(e,!0)}},model:{value:t.formData.type1id,callback:function(e){t.$set(t.formData,"type1id",e)},expression:"formData.type1id"}})]},proxy:!0}])}),e("van-field",{attrs:{size:"large",label:"大类",readonly:""},scopedSlots:t._u([{key:"input",fn:function(){return[e("custom-picker",{attrs:{columns:t.columns.type2Options,placeholder:"请选择",disabled:t.isDetail},on:{confirm:function(e){return t.handleType2Change(e,!0)}},model:{value:t.formData.type2id,callback:function(e){t.$set(t.formData,"type2id",e)},expression:"formData.type2id"}})]},proxy:!0}])}),e("van-field",{attrs:{size:"large",label:"小类",readonly:""},scopedSlots:t._u([{key:"input",fn:function(){return[e("custom-picker",{attrs:{columns:t.columns.type3Options,placeholder:"请选择",disabled:t.isDetail},on:{confirm:function(e){return t.handleType3Change(e,!0)}},model:{value:t.formData.type3id,callback:function(e){t.$set(t.formData,"type3id",e)},expression:"formData.type3id"}})]},proxy:!0}])}),e("van-field",{attrs:{size:"large",label:"立案标准",readonly:""},scopedSlots:t._u([{key:"input",fn:function(){return[e("custom-picker",{attrs:{columns:t.columns.type4Options,placeholder:"请选择",disabled:t.isDetail},on:{confirm:function(e){return t.onPickerConfirm("type4id",e)}},model:{value:t.formData.type4id,callback:function(e){t.$set(t.formData,"type4id",e)},expression:"formData.type4id"}})]},proxy:!0}])})],1),e("van-cell-group",{staticClass:"form-group"},[e("van-field",{attrs:{size:"large",label:"位置选择",readonly:"",placeholder:"","right-icon":"arrow"},scopedSlots:t._u([{key:"right-icon",fn:function(){return[e("div",{staticClass:"section-content"},[e("van-button",{staticClass:"action-btn",attrs:{plain:"",type:"primary",size:"small",disabled:t.isDetail},on:{click:function(e){!t.isDetail&&(t.showMapDialog=!0)}}},[t._v(t._s(t.formData.areaid?"已定位":"选择定位"))])],1)]},proxy:!0}])}),e("van-field",{attrs:{size:"large",label:"位置描述",readonly:"",placeholder:"","right-icon":"arrow"},scopedSlots:t._u([{key:"right-icon",fn:function(){return[e("div",{staticClass:"section-content"},[e("van-button",{staticClass:"action-btn",attrs:{plain:"",type:"primary",size:"small",disabled:t.isDetail},on:{click:function(e){!t.isDetail&&t.generateAddress()}}},[t._v(t._s(t.formData.address?"已生成":"自动生成"))])],1)]},proxy:!0}])}),e("van-field",{attrs:{type:"textarea",label:"",placeholder:"请输入位置描述",rows:"2",autosize:"",maxlength:"50","show-word-limit":"",readonly:t.isDetail},model:{value:t.formData.address,callback:function(e){t.$set(t.formData,"address",e)},expression:"formData.address"}}),e("van-field",{attrs:{size:"large",label:"问题描述",readonly:"",placeholder:"","right-icon":"arrow"},scopedSlots:t._u([{key:"right-icon",fn:function(){return[e("div",{staticClass:"section-content"},[e("idioms-selector",{attrs:{disabled:t.isDetail},on:{select:t.selectIdiom}},[t._v("选择惯用语")])],1)]},proxy:!0}])}),e("van-field",{attrs:{type:"textarea",label:"",placeholder:"请输入问题描述",rows:"2",autosize:"",maxlength:"50","show-word-limit":"",readonly:t.isDetail},model:{value:t.formData.eventdesc,callback:function(e){t.$set(t.formData,"eventdesc",e)},expression:"formData.eventdesc"}}),e("div",{staticClass:"form-section"},[e("div",{staticClass:"section-label"},[t._v("图片选择（最少一张）")])]),e("div",{staticClass:"uploader-wrapper"},[e("vantFileUpload",{attrs:{"file-type":["jpg","png"],disabled:t.isDetail},on:{change:t.handleFileChange},model:{value:t.formData.fileStr,callback:function(e){t.$set(t.formData,"fileStr",e)},expression:"formData.fileStr"}})],1),e("van-field",{attrs:{size:"large",label:"是否自办结",readonly:"",placeholder:"","right-icon":"arrow"},scopedSlots:t._u([{key:"right-icon",fn:function(){return[e("van-radio-group",{attrs:{direction:"horizontal",disabled:t.isDetail},model:{value:t.formData.isSelfComplete,callback:function(e){t.$set(t.formData,"isSelfComplete",e)},expression:"formData.isSelfComplete"}},[e("van-radio",{attrs:{name:1}},[t._v("是")]),e("van-radio",{attrs:{name:0}},[t._v("否")])],1)]},proxy:!0}])})],1)],1),e("div",{staticClass:"submit-btn-wrapper"},[e("van-button",{attrs:{type:"info",block:""},on:{click:function(e){t.isDetail?t.onClickLeft():t.onSubmit()}}},[t._v(t._s(t.isDetail?"返回":"提交"))])],1),e("van-popup",{style:{height:"80%",width:"100%"},attrs:{position:"bottom"},model:{value:t.showMapDialog,callback:function(e){t.showMapDialog=e},expression:"showMapDialog"}},[e("div",{staticClass:"map-dialog"},[t.showMapDialog?e("Map",{ref:"mapComponent",on:{locationSelected:t.handleLocationSelected}}):t._e(),e("div",{staticClass:"map-dialog-footer"},[e("van-button",{attrs:{type:"info",block:""},on:{click:t.confirmLocation}},[t._v("确认位置")])],1)],1)])],1)},s=[],l=(i("e9f5"),i("f665"),i("ab43"),i("8080")),o=i("eeb8"),n=i("5945"),r=i("207e"),d=i("6e29"),c=i("2934"),p=(i("4260"),{name:"ProblemReport",components:{CustomPicker:l["a"],TabSwitch:o["a"],Map:n["a"],vantFileUpload:r["a"],IdiomsSelector:d["a"]},data(){return{showMapDialog:!1,detailInfo:null,isDetail:!1,formData:{source:"",createid:"",type1id:"",type2id:"",type3id:"",type4id:"",areaid:"",streetid:"",address:"",eventdesc:"",fileStr:"",isSelfComplete:0},columns:{type1Options:[{text:"类型1",value:"1"},{text:"类型2",value:"2"},{text:"类型3",value:"3"}],type2Options:[{text:"大类1",value:"1"},{text:"大类2",value:"2"},{text:"大类3",value:"3"}],type3Options:[{text:"小类1",value:"1"},{text:"小类2",value:"2"},{text:"小类3",value:"3"}],type4Options:[{text:"标准1",value:"1"},{text:"标准2",value:"2"},{text:"标准3",value:"3"}]},areaOptions:[]}},created(){},mounted(){this.getDictsList(),this.initParams(),this.checkDetailMode()},methods:{async checkDetailMode(){const t=this.$route.query.detailId;if(t){this.isDetail=!0,this.$toast.loading({message:"加载详情...",forbidClick:!0,duration:0});try{const e=await Object(c["k"])(t);200===e.code?(this.detailInfo=e.data,this.fillFormData(e.data)):this.$toast.fail(e.msg||"获取详情失败")}catch(e){console.error("获取详情失败:",e),this.$toast.fail("获取详情失败")}finally{this.$toast.clear()}}},fillFormData(t){this.formData.type1id=t.type1id||"",this.formData.areaid=t.areaid||"",this.formData.streetid=t.streetid||"",this.formData.address=t.address||"",this.formData.eventdesc=t.eventdesc||"",this.formData.isSelfComplete=t.isSelfComplete||0,this.formData.fileStr=t.fileStr||"",t.type1id&&(this.handleType1Change(t.type1id,!1),setTimeout(()=>{this.formData.type2id=t.type2id||"",t.type2id&&(this.handleType2Change(t.type2id,!1),setTimeout(()=>{this.formData.type3id=t.type3id||"",t.type3id&&(this.handleType3Change(t.type3id,!1),setTimeout(()=>{this.formData.type4id=t.type4id||""},300))},300))},300))},initParams(){this.formData.source="2",this.formData.createid=this.user.nickName},getDictsList(){this.getDicts("zhcg_wtlx").then(t=>{this.columns.type1Options=t.data.map(t=>({text:t.dictLabel,value:t.dictValue})),this.isDetail&&this.detailInfo&&this.fillFormData(this.detailInfo)}),this.getDicts("county").then(t=>{this.areaOptions=t.data.map(t=>({label:t.dictLabel,value:t.dictValue}))})},handleType1Change(t,e){t?this.getDicts(t.value?t.value:t).then(t=>{this.columns.type2Options=t.data.map(t=>({text:t.dictLabel,value:t.dictValue})),e&&(this.formData.type2id=this.columns.type2Options[0].value),this.handleType2Change(this.formData.type2id,!!e)}):(this.columns.type2Options=[],this.formData.type2id="",this.handleType2Change(this.formData.type2id,!1))},handleType2Change(t,e){t?this.getDicts(t.value?t.value:t).then(t=>{this.columns.type3Options=t.data.map(t=>({text:t.dictLabel,value:t.dictValue})),e&&(this.formData.type3id=this.columns.type3Options[0].value),this.handleType3Change(this.formData.type3id,!!e)}):(this.columns.type3Options=[],this.formData.type3id="",this.handleType3Change(this.formData.type3id,!1))},handleType3Change(t,e){t?this.getDicts(t.value?t.value:t).then(t=>{this.columns.type4Options=t.data.map(t=>({text:t.dictLabel,value:t.dictValue})),e&&(this.formData.type4id=this.columns.type4Options[0].value)}):(this.columns.type4Options=[],this.formData.type4id="")},onClickLeft(){this.$router.go(-1)},onPickerConfirm(t,e){console.log(t+" selected:",e)},handleLocationSelected(t){this.formData.areaid=this.areaOptions.find(e=>e.label==t.addressComponent.county).value?this.areaOptions.find(e=>e.label==t.addressComponent.county).value:"",this.formData.streetid=t.addressComponent.road,this.formData.address=t.formatted_address},confirmLocation(){this.showMapDialog=!1},onSubmit(){return this.formData.type1id?this.formData.type2id?this.formData.type3id?this.formData.type4id?this.formData.address?this.formData.eventdesc?0===this.formData.fileStr.length?this.$toast("请至少上传一张图片"):(this.$toast.loading({message:"提交中...",forbidClick:!0}),this.formData.isShow=Number(this.$route.query.taskType),this.formData.specialTaskId=Number(this.$route.query.id),this.isDetail&&this.detailInfo&&(this.formData.id=this.detailInfo.id),void Object(c["a"])(this.formData).then(t=>{200==t.code?(this.$toast.success("提交成功"),setTimeout(()=>{this.$router.go(-2)},1e3)):this.$toast.fail(t.msg||"提交失败")}).catch(t=>{console.error("提交失败:",t),this.$toast.fail("提交失败")}).finally(()=>{this.$toast.clear()})):this.$toast("请输入问题描述"):this.$toast("请输入位置描述"):this.$toast("请选择立案标准"):this.$toast("请选择小类"):this.$toast("请选择大类"):this.$toast("请选择类型")},selectIdiom(t){this.formData.eventdesc=t},handleFileChange(t){console.log("文件已更新:",t)},generateAddress(){if(!this.formData.areaid)return void this.$toast("请先选择位置");const t=this.formData.areaid||"",e=this.formData.streetid||"";this.formData.address=`${t}${e}附近`,this.$toast("地址已自动生成")}}}),u=p,h=(i("3a67"),i("2877")),f=Object(h["a"])(u,a,s,!1,null,"42fc4b3d",null);e["default"]=f.exports},c3d6:function(t,e,i){"use strict";i("21a5")},eeb8:function(t,e,i){"use strict";var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"tab-switch"},[e("van-tabs",{attrs:{border:!1,"line-width":20,"line-height":"2px",color:"#1989fa","title-active-color":"#1989fa","title-inactive-color":"#666"},on:{change:t.handleChange},model:{value:t.modelValue,callback:function(e){t.modelValue=e},expression:"modelValue"}},t._l(t.tabs,(function(t,i){return e("van-tab",{key:i,attrs:{title:t.label,name:t.value}})})),1)],1)},s=[],l={name:"TabSwitch",props:{tabs:{type:Array,default:()=>[]},value:{type:[String,Number],default:""}},computed:{modelValue:{get(){return this.value},set(t){this.$emit("input",t)}}},methods:{handleChange(t){this.$emit("change",t)}}},o=l,n=(i("3224"),i("2877")),r=Object(n["a"])(o,a,s,!1,null,"185e5f8f",null);e["a"]=r.exports},f833:function(t,e,i){}}]);