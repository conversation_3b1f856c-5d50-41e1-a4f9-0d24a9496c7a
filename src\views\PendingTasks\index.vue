<!--待处置任务-->
<template>
  <div class="todo-page">
    <!-- 导航栏 -->
    <van-nav-bar
        title="待处置任务"
        left-arrow
        fixed
        @click-left="onClickLeft"
    >
    </van-nav-bar>

    <!-- 标签切换 -->
    <div class="tab-fixed">
      <tabSwitch
          v-model="queryParams.searchType"
          :tabs="tabOptions"
          @change="handleTabChange"
      />
    </div>

    <!-- 任务列表 -->
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list
          v-model="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad"
      >
        <div class="todo-list">
          <van-cell-group v-for="(item, index) in List" :key="index">
            <van-cell class="todo-item" @click="getTaskDetail(item)">
              <!-- 主要内容 -->
              <div class="todo-content">
                <!-- 来源信息 -->
                <div class="source-info">
                  <div class="source-info-left">
                    <!-- 图标 -->
                    <div class="source-icon">
                      <van-icon :name="titleIcon" size="30" />
                    </div>
                    <span class="source-label">问题来源：</span>
                    <span class="source-value">{{item.source}}</span>
                  </div>
                  <div class="source-info-right">
                    <van-button type="info" class="rlBtn" @click.stop="handleClaim(item)">{{ queryParams.searchType == 1?'认领':'处理' }}</van-button>
                  </div>
                </div>

                <div class="todo-container">
                  <div class="todo-container-left">
                    <van-image width="68" height="68" :src="item.image" fit="cover" radius="5"/>
                  </div>
                  <div class="todo-container-right">
                    <!-- 标题 -->
                    <div class="todo-title">
                      {{ item.title }}
                    </div>

                    <!-- 时间信息 -->
                    <div class="todo-info">
                      <span class="time">{{ item.time }}</span>
                    </div>

                    <!-- 内容 -->
                    <div class="todo-title">
                      {{ item.content }}
                    </div>

                    <!-- 倒计时 -->
                    <div class="countdown">
                      <span class="countdown-label">剩余：{{ item.countdown }}</span>
                      <template v-if="item.urgent">
                        <span class="urgent-tag">紧急</span>
                      </template>
                    </div>
                  </div>
                </div>
              </div>
            </van-cell>
          </van-cell-group>
        </div>
      </van-list>
    </van-pull-refresh>
  </div>
</template>

<script>
import tabSwitch from "@/components/TabSwitch";
import { getProblemList,addAjcl } from "@/api/common";

export default {
  name: 'index',
  components: {
    tabSwitch
  },
  data() {
    return {
      queryParams: {
        // 当前选中的标签
        searchType: 1,
        pageSize: 10,
        pageNum: 1
      },
      // 标签选项
      tabOptions: [
        { label: '部门', value: 1 },
        { label: '个人', value: 2 }
      ],
      List: [],
      titleIcon: require('@/assets/ToDoTasks/square.png'),
      // 下拉刷新相关
      refreshing: false,
      // 上拉加载相关
      loading: false,
      finished: false,
      // 总数据量
      total: 0,
      // 添加 loading 状态
      isLoading: false,
      sourceOptions: []
    }
  },
  mounted() {
    this.init()
  },
  methods: {

    async init() {
      await this.getDictsList()
      // 初始加载
      await this.getList();
    },

    async getDictsList() {
      //问题来源
      return new Promise((resolve) => {
        this.getDicts('zhcg_wtly').then((response) => {
          this.sourceOptions = response.data.map(item => ({
            label: item.dictLabel,
            value: item.dictValue
          }));
          resolve();
        });
      });
    },

    // 获取列表数据
    async getList() {
      // 如果正在加载中，则不重复请求
      if (this.isLoading) return;

      try {
        this.isLoading = true;

        // 显示加载提示
        this.$toast.loading({
          message: '加载中...',
          forbidClick: true,
          duration: 0
        });

        // 模拟接口请求
        const res = await getProblemList({...this.queryParams})

        this.total = res.total;

        // 如果是刷新，清空列表
        if (this.refreshing) {
          this.List = [];
        }

        // 追加数据
        this.List.push(...res.rows.map(item => ({
          id: item.id,
          source:this.getDictText('sourceOptions',item.source),
          title: item.type4id,
          time: item.createtime,
          countdown: item.remainTime,
          content: item.eventdesc,
          urgent: item.urgent == 1?true:false,
          image: item.fileStr?this.$getImageUrl(item.fileStr):''
        })));

        // 判断是否加载完成
        this.finished = this.List.length >= this.total;

        // 加载状态结束
        this.loading = false;

        // 刷新状态结束
        if (this.refreshing) {
          this.refreshing = false;
        }

        // 关闭加载提示
        this.$toast.clear();

      } catch (error) {
        console.error('获取列表失败:', error);
        this.$toast.fail('获取列表失败');
        // 发生错误时也要关闭加载提示
        this.$toast.clear();
      } finally {
        // 确保 loading 状态被重置
        this.isLoading = false;
      }
    },

    //前往详情页
    getTaskDetail(item) {
      this.$router.push({
        name: 'PendingTasksDetail',
        query: {
          id: item.id
        }
      })
    },

    // 下拉刷新
    async onRefresh() {
      // 重置页码
      this.queryParams.pageNum = 1;
      // 重置加载状态
      this.finished = false;
      // 重新加载数据
      await this.getList();
    },

    // 上拉加载更多
    onLoad() {
      // 页码加1
      this.queryParams.pageNum += 1;
      // 加载数据
      this.getList();
    },

    // 标签切换
    handleTabChange(value) {
      // 重置页码
      this.queryParams.pageNum = 1;
      // 重置列表
      this.List = [];
      // 重置加载状态
      this.finished = false;
      // 重新加载数据
      this.getList();
    },

    //认领
    handleClaim(item) {
      if (this.queryParams.searchType == 1) {
        this.$dialog.confirm({
          title: '确认认领',
          message: '确定要认领该任务吗？',
          confirmButtonText: '确认认领',
          cancelButtonText: '取消'
        }).then(() => {
          // 确认后执行认领操作
          this.$toast.loading({
            message: '认领中...',
            forbidClick: true,
            duration: 0
          });

          addAjcl({eventId: item.id, operateType: 6}).then(res => {
            this.$toast.clear();
            if (res.code == 200) {
              setTimeout(() => {
                this.$toast.success('认领成功');
              },1000)
              // 刷新列表
              this.handleTabChange();
            } else {
              this.$toast.fail(res.msg || '认领失败');
            }
          }).catch(err => {
            this.$toast.clear();
            this.$toast.fail('认领失败');
            console.error('认领失败:', err);
          });
        }).catch(() => {
          // 取消认领，不做任何操作
        });
      } else {
        this.getTaskDetail(item)
      }
    },

    onClickLeft() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.todo-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-top: 90px; // 为固定定位的导航栏和标签栏预留空间
}

// 标签栏固定定位
.tab-fixed {
  position: fixed;
  top: 46px; // 导航栏高度
  left: 0;
  right: 0;
  z-index: 99;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

// 任务列表样式
.todo-list {
  margin-top: 12px;

  .van-cell-group {
    margin-bottom: 12px;
    border-radius: 8px;
    overflow: hidden;
  }
}

// 任务项样式
.todo-item {

  ::v-deep .van-cell__value {
    flex: 1;
  }

  .source-icon {
    width: 32px;
    height: 32px;
    background-color: #e8f3ff;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    margin-left: 16px;
  }

  .todo-content {
    flex: 1;

    .source-info {
      width: 100%;
      height: 49px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 14px;
      color: #666;
      border-bottom: 1px solid #DADFE8;
      margin-bottom: 8px;

      .source-info-left {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .source-label,.source-value {
          font-family: Source Han Sans, Source Han Sans;
          font-weight: 700;
          font-size: 19px;
          color: #333333;
          line-height: 21px;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
      }
      .source-info-right {
        margin-right: 17px;
        .rlBtn {
          width: 56px;
          height: 27px;
          border-radius: 5px;
          padding: unset;
        }
      }
    }

    .todo-container {
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
      .todo-container-left {
        margin: 4px 16px 0 16px;
      }
      .todo-container-right {
        .todo-title {
          font-family: Source Han Sans, Source Han Sans;
          font-weight: 400;
          font-size: 15px;
          color: #333333;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }

        .todo-info {
          font-size: 13px;
          color: #999;

          .count {
            margin-left: 12px;
          }
        }

        .countdown {
          font-size: 13px;
          color: #ff4d4f;
          margin-top: 4px;
          margin-bottom: 13px;

          .countdown-label {
            height: 26px;
            background: #FFE3E3;
            color: #FC4242;
            padding: 5px 8px;
            border-radius: 5px;
          }

          .urgent-tag {
            display: inline-block;
            padding: 2px 6px;
            background: #FFE3E3;
            color: #FC4242;
            margin-left: 8px;
            font-size: 12px;
            border-radius: 5px;
          }
        }
      }
    }
  }
}

::v-deep .van-cell {
  padding: unset;
}

// Tab样式调整
::v-deep .van-tabs {
  .van-tabs__wrap {
    padding: 0 12px;
    background-color: #fff;
  }

  .van-tab {
    color: #666;
    font-size: 14px;

    &--active {
      color: #1989fa;
      font-weight: 500;
    }
  }

  .van-tabs__line {
    background-color: #1989fa;
    width: 20px !important;
    left: 50%;
    transform: translateX(-50%);
  }
}

// 修改下拉刷新和加载更多的样式
.van-pull-refresh {
  min-height: calc(100vh - 90px); // 减去导航栏和标签栏的总高度
}

.van-list {
  min-height: 100%;
}

// 优化滚动区域
.van-pull-refresh__track {
  min-height: 100%;
}
</style>

