(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2b8b74c8"],{"74ff":function(t,a,s){},e332:function(t,a,s){"use strict";s.r(a);var e=function(){var t=this,a=t._self._c;return a("div",{staticClass:"detail-page"},[a("van-nav-bar",{attrs:{title:"专项整治","left-arrow":"",fixed:""},on:{"click-left":t.onClickLeft}}),a("div",{staticClass:"content-container"},[a("div",{staticClass:"info-section"},[a("div",{staticClass:"info-item"},[a("span",{staticClass:"label"},[t._v("任务编号")]),a("span",{staticClass:"value"},[t._v(t._s(t.taskInfo.taskCode||""))])]),a("div",{staticClass:"info-item"},[a("span",{staticClass:"label"},[t._v("任务类型")]),a("span",{staticClass:"value"},[t._v(t._s(t.taskInfo.taskType||""))])]),a("div",{staticClass:"info-item"},[a("span",{staticClass:"label"},[t._v("任务级别")]),a("span",{staticClass:"value",class:{urgent:"紧急"==t.taskInfo.taskLevel}},[t._v(t._s(t.taskInfo.taskLevel||""))])]),a("div",{staticClass:"info-item"},[a("span",{staticClass:"label"},[t._v("开始时间")]),a("span",{staticClass:"value"},[t._v(t._s(t.taskInfo.startTime||""))])]),a("div",{staticClass:"info-item"},[a("span",{staticClass:"label"},[t._v("结束时间")]),a("span",{staticClass:"value"},[t._v(t._s(t.taskInfo.endTime||""))])])]),a("div",{staticClass:"title-section"},[a("h3",{staticClass:"section-title"},[t._v("任务标题")]),a("div",{staticClass:"content-text"},[t._v(t._s(t.taskInfo.title||""))])]),a("div",{staticClass:"description-section"},[a("h3",{staticClass:"section-title"},[t._v("任务描述")]),a("div",{staticClass:"content-text"},[t._v(t._s(t.taskInfo.description||""))])]),a("div",{staticClass:"remark-section"},[a("h3",{staticClass:"section-title"},[t._v("备注")]),a("div",{staticClass:"content-text"},[t._v(t._s(t.taskInfo.remark||""))])])]),a("div",{staticClass:"footer-buttons"},[a("van-button",{staticClass:"btn-publish",attrs:{type:"primary",color:"#1989fa",block:""},on:{click:t.onPublishDraft}},[t._v("发布案卷")])],1)],1)},i=[],l=(s("14d9"),s("2934")),n={name:"SpecialRectificationDetail",data(){return{taskId:"",typeData:[{value:2,label:"普查"},{value:1,label:"整治"}],levelData:[{value:1,label:"普通"},{value:2,label:"紧急"}],taskInfo:{taskCode:"",taskType:"",taskLevel:"",problemType:"",startTime:"",endTime:"",title:"",description:"",remark:""},taskType:""}},created(){this.taskId=this.$route.query.id,this.taskId&&this.getTaskDetail()},methods:{async getTaskDetail(){try{this.$toast.loading({message:"加载中...",forbidClick:!0,duration:0});const t=await Object(l["m"])(this.taskId);console.log(t,"getSpecialTaskDetail"),200===t.code?(this.taskInfo={taskCode:t.data.taskNumber,taskType:this.getDictText("typeData",t.data.taskType),taskLevel:this.getDictText("levelData",t.data.taskLevel),problemType:"暂无数据",startTime:t.data.startTime,endTime:t.data.endTime,title:t.data.taskTitle,description:t.data.taskDesc,remark:t.data.remark},this.taskType=t.data.taskType):this.$toast.fail(t.msg||"获取详情失败"),setTimeout(()=>{this.$toast.clear()},500)}catch(t){console.error("获取任务详情失败:",t),this.$toast.fail("获取详情失败")}},onClickLeft(){this.$router.go(-1)},onPublishDraft(){this.$router.push({name:1==this.taskType?"SpecialRectificationSubmitZz":"SpecialRectificationSubmit",query:{id:this.taskId,taskType:this.taskType}})}}},c=n,o=(s("eee4"),s("2877")),r=Object(o["a"])(c,e,i,!1,null,"37ef680e",null);a["default"]=r.exports},eee4:function(t,a,s){"use strict";s("74ff")}}]);