import request from '@/util/request'

// 获取上下班规定时间  返回字段: workStartTime上班时间 workEndTime下班时间
export function getClockTime() {
  return request({
    url: '/collectors/late/select',
    method: 'get',
  })
}

// 签到或签退  请求字段
//   "nowLongitude": "2",  经度
//   "nowLatitude": "2",  纬度
//   //1为签到 2为签退 3为上报
//   "status": 2,
//   "remark":"asdasd", 备注信息
//   "filePath":"adsasd"  图片信息
export function signOrOut(data) {
  return request({
    url: '/collectors/attendance/signOrOut',
    method: 'post',
    data
  })
}

//当前打卡信息查询 传:userId    取值this.user.userId
export function getClockInfo(params) {
  return request({
    url: '/collectors/attendance/select',
    method: 'get',
    params
  })
}
