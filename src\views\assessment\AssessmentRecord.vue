<template>
  <div class="assessment-record">
    <!-- 标题栏 -->
    <van-nav-bar title="考核登记" left-arrow @click-left="onClickLeft" fixed style="position: fixed;" />

    <!-- 主要内容区域 -->
    <div class="content">
      <!-- 路段选择 -->
      <van-cell-group>
        <van-cell title="路段" :value="selectedRoad || '请选择'" is-link @click="showRoadPicker = true" />
      </van-cell-group>

      <!-- 考核内容区域 -->
      <template v-if="selectedRoad">
        <van-tabs v-model="activeTab">
          <van-tab v-for="(tab, index) in subtract" :key="index" :title="tab.indexName">
            <van-cell-group>
              <van-cell
                v-for="(item, itemIndex) in tab.roadConfigMinorVOS"
                :key="itemIndex"
                :title="item.assessText"
                is-link
                center
                @click="showViolationSheet(item, tab)"
              >
                <template #right-icon>
                  <van-button type="info" size="small" plain>登记违规</van-button>
                </template>
              </van-cell>
            </van-cell-group>
          </van-tab>
        </van-tabs>
      </template>

      <!-- 底部提交按钮 -->
      <div class="submit-bar" v-if="selectedRoad">
        <div class="recorded-count" @click="showRecordedList">已登记 {{ recordedCount }} 条</div>
        <van-button type="info" @click="handleSubmit">提交</van-button>
      </div>
    </div>

    <!-- 路段选择弹出层 -->
    <van-popup v-model="showRoadPicker" position="bottom" round safe-area-inset-bottom>
      <van-picker
        show-toolbar
        :columns="roads"
        @confirm="onRoadConfirm"
        @cancel="showRoadPicker = false"
        title="路段"
      />
    </van-popup>

    <!-- 已登记记录弹出层 -->
    <van-popup 
      v-model:show="showRecorded" 
      position="bottom" 
      round 
      :style="{ maxHeight: '60%', height: '60%' }"
    >
      <div class="popup-content">
        <van-nav-bar 
          title="已登记记录" 
          left-arrow 
          @click-left="showRecorded = false" 
        />
        <van-cell-group>
          <van-cell
            v-for="(record, index) in recordedList"
            :key="index"
            :label="`${record.bigIndex} - ${record.smallIndex}`"
            :title="record.inText || '暂无描述'"
          >
            <template #value>
              <div class="record-time">{{ record.timeIn || '-' }}</div>
            </template>
          </van-cell>
        </van-cell-group>
      </div>
    </van-popup>

    <!-- 违规登记弹出层 -->
    <van-popup v-model="showViolationPopup" position="bottom" round safe-area-inset-bottom :style="{ height: '80%' }">
      <div class="violation-popup">
        <van-nav-bar title="登记违规" left-arrow @click-left="showViolationPopup = false" />
        <div class="violation-content">
          <!-- 信息展示 -->
          <van-cell-group>
            <van-cell title-class="title-class" title="路段" :value="selectedRoad" />
            <van-cell title-class="title-class" title="大类" :value="currentMajorType" />
            <van-cell title-class="title-class" title="小类" :value="currentMinorType" />
          </van-cell-group>

          <!-- 问题描述 -->
          <van-field
            label-class="title-class"
            v-model="description"
            type="textarea"
            placeholder="请输入问题描述"
            label="问题描述"
            :autosize="{ minHeight: 100, maxHeight: 500 }"
          />

          <!-- 图片上传 -->
          <div class="upload-section">
            <div class="upload-title">图片选择（最少一张）</div>
            <van-uploader v-model="fileList" :max-count="9" :after-read="afterRead" multiple>
              <div class="upload-trigger">
                <van-icon name="photograph" size="24" />
              </div>
            </van-uploader>
          </div>

          <!-- 底部保存按钮 -->
          <div class="submit-bar">
            <van-button type="info" block @click="handleSave">保存</van-button>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { addCheck, getAssessDetail, getSubtract, startCheck } from '@/api/assessment'
import { Dialog, Toast } from 'vant'

export default {
  name: 'AssessmentRecord',
  data() {
    return {
      showRoadPicker: true,
      selectedRoad: '',
      activeTab: 0,
      recordedCount: 0,
      roads: [],
      detail: null,
      subtract: [],
      // 违规登记相关
      showViolationPopup: false,
      currentMajorType: '',
      currentMinorType: '',
      description: '',
      fileList: [],
      currentItem: null,
      selectedRoadId: null,
      // 已登记记录相关
      showRecorded: false,
      recordedList: [],
    }
  },
  created() {
    const id = this.$route.query.id

    if (id) {
      this.getDetail(id).then((res) => {
        if (res.status === 1) {
          startCheck({ id, status: 2 })
        }
      })
    }
  },
  methods: {
    onClickLeft() {
      this.$router.back()
    },
    onRoadConfirm(value) {
      this.selectedRoad = value
      const selectRoad = this.detail.roadAssessMinorVOS.find((item) => item.roadName === value)
      console.log('selectRoad', selectRoad)
      const roadId = selectRoad.roadId
      this.selectedRoadId = selectRoad.id
      console.log('roadId', roadId)
      this.fetchSubtract(roadId)

      const selectVos = this.detail.roadAssessMinorVOS.find((item) => item.id === selectRoad.id).roadCheckVOS

      console.log('selectVos', selectVos)
      this.recordedList = selectVos
      this.recordedCount = selectVos.length
      this.showRoadPicker = false
    },
    async fetchSubtract(roadId) {
      const res = await getSubtract({ roadId })
      if (res.code === 200) {
        this.subtract = res.data
      }
    },
    showViolationSheet(item, tab) {
      this.currentItem = item
      this.currentMajorType = tab.indexName
      this.currentMinorType = item.assessText
      this.showViolationPopup = true
      // 重置表单
      this.description = ''
      this.fileList = []
    },
    afterRead(file) {
      // 处理图片上传后的逻辑
      console.log('文件上传:', file)
    },
    handleSave() {
      // 表单验证
      if (!this.description.trim()) {
        this.$toast('请输入问题描述')
        return
      }

      const data = {
        minorId: this.selectedRoadId,
        bigIndex: this.currentMajorType,
        smallIndex: this.currentItem.assessText,
        num: this.currentItem.num,
        filePath: null,
        inText: this.description,
      }

      addCheck(data).then((res) => {
        if (res.code === 200) {
          this.$toast.success('保存成功')
          // 更新已登记列表
          this.recordedList.push({
            ...data,
            timeIn: new Date().toLocaleString()
          })
        }
      })
      this.showViolationPopup = false
      this.recordedCount++
    },
    handleSubmit() {
      Dialog.confirm({
        title: '',
        message: '您是最后提交人，是否考核结束?',
      })
        .then(() => {
          Toast.loading({
            duration: 0, // 持续展示 toast
            forbidClick: true,
          })
          // on confirm
          startCheck({ id: this.$route.query.id, status: 3 }).then((res) => {
            Toast.clear()
          })
        })
        .catch(() => {
          // on cancel
        })
      // 处理提交
      this.$toast('提交成功')
    },
    getDetail(id) {
      return getAssessDetail({ id })
        .then((res) => {
          if (res.code === 200) {
            this.detail = res.data
            this.roads = res.data.roadAssessMinorVOS.map((item) => item.roadName)
            return res.data
          }
        })
        .catch((error) => {
          console.error('获取详情失败:', error)
          this.$toast('获取详情失败')
        })
    },
    showRecordedList() {
      this.showRecorded = true
    },
  },
}
</script>

<style lang="scss" scoped>
.assessment-record {
  min-height: 100vh;
  background: #f7f8fa;
  padding-top: 46px;

  .content {
    padding-bottom: 80px;
  }

  .submit-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    padding: 10px 16px;
    box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    justify-content: space-between;

    .recorded-count {
      text-align: center;
      color: #666;
      padding: 8px 12px;
      border-radius: 4px;
      cursor: pointer;
      
      &:active {
        background: rgba(0, 0, 0, 0.05);
      }
    }
  }

  :deep(.van-cell) {
    align-items: center;
  }

  .violation-popup {
    height: 100%;
    display: flex;
    flex-direction: column;

    .violation-content {
      flex: 1;
      overflow-y: auto;
      padding: 16px;
      padding-bottom: 80px;

      .upload-section {
        margin-top: 16px;
        background: #fff;
        padding: 16px;
        border-radius: 8px;

        .upload-title {
          margin-bottom: 16px;
          color: #323233;
          font-size: 14px;
        }

        .upload-trigger {
          width: 100px;
          height: 100px;
          background: #f7f8fa;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 4px;
        }
      }
    }

    .submit-bar {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      background: #fff;
      padding: 10px 16px;
      box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.05);
    }
  }

  .popup-content {
    .record-time {
      font-size: 12px;
      color: #999;
    }
  }
}
</style>

<style>
.title-class {
  font-size: 14px;
  color: #323233;
  width: 80px;
  flex: none !important;
}
</style>
