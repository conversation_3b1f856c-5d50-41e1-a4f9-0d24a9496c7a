(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5edda94b"],{"512b":function(s,t,a){"use strict";a.r(t);var i=function(){var s=this,t=s._self._c;return t("div",{staticClass:"assessment-list"},[t("van-nav-bar",{staticStyle:{position:"fixed"},attrs:{title:"路段考核","left-arrow":"",fixed:""},on:{"click-left":s.onClickLeft}}),t("div",{staticClass:"tabs-container"},[t("van-tabs",{attrs:{sticky:"","offset-top":"46px"},model:{value:s.activeTab,callback:function(t){s.activeTab=t},expression:"activeTab"}},[t("van-tab",{attrs:{title:"我的任务",name:"1"}},[t("van-pull-refresh",{on:{refresh:s.onRefresh},model:{value:s.refreshing,callback:function(t){s.refreshing=t},expression:"refreshing"}},[t("van-list",{attrs:{finished:s.finished,"finished-text":"没有更多了"},on:{load:s.onLoad},model:{value:s.loading,callback:function(t){s.loading=t},expression:"loading"}},s._l(s.taskList,(function(i){return t("div",{key:i.id,staticClass:"task-item"},[t("div",{staticClass:"task-header"}),t("div",{staticClass:"task-icon"},[t("img",{staticClass:"task-icon-img",attrs:{src:a("e5bc"),alt:"任务图标"}}),t("div",{staticClass:"task-title"},[s._v(s._s(i.assessName))])]),t("van-divider",{staticClass:"w-full"}),t("div",{staticClass:"task-content"},[t("div",{staticClass:"task-info"},[t("div",{staticClass:"info-item"},[t("div",{staticClass:"label"},[s._v("【备检路段】")]),t("div",{staticClass:"value"},[s._v(s._s(i.checkRoads||"无"))])]),t("div",{staticClass:"info-item"},[t("div",{staticClass:"label"},[s._v("【人行道范围】")]),t("div",{staticClass:"value"},[s._v(s._s(i.walkingRange||"无"))])]),t("div",{staticClass:"info-item"},[t("div",{staticClass:"label"},[s._v("【完成时间】")]),t("div",{staticClass:"value"},[s._v(s._s(i.timeEnd))])]),t("div",{staticClass:"info-item"},[t("div",{staticClass:"label"},[s._v("【检查状态】")]),t("div",{staticClass:"status"},[s._v(s._s(s.getStatus(i.status)))])])])]),t("van-divider",{staticClass:"w-full"}),t("div",{staticClass:"task-footer"},[t("div",{staticClass:"info-item"},[t("span",{staticClass:"label"},[s._v("发布时间：")]),t("span",[s._v(s._s(i.publishTime))])]),t("van-button",{attrs:{type:"info",size:"mini",plain:""},on:{click:function(t){return s.goToRecord(i)}}},[s._v("开始任务")])],1)],1)})),0)],1)],1),t("van-tab",{attrs:{title:"历史任务",name:"2"}},[t("van-pull-refresh",{on:{refresh:s.onHistoryRefresh},model:{value:s.historyRefreshing,callback:function(t){s.historyRefreshing=t},expression:"historyRefreshing"}},[t("van-list",{attrs:{finished:s.historyFinished,"finished-text":"没有更多了"},on:{load:s.onHistoryLoad},model:{value:s.historyLoading,callback:function(t){s.historyLoading=t},expression:"historyLoading"}},s._l(s.historyTaskList,(function(i){return t("div",{key:i.id,staticClass:"task-item"},[t("div",{staticClass:"task-header"}),t("div",{staticClass:"task-icon"},[t("img",{staticClass:"task-icon-img",attrs:{src:a("e5bc"),alt:"任务图标"}}),t("div",{staticClass:"task-title"},[s._v(s._s(i.assessName))])]),t("van-divider",{staticClass:"w-full"}),t("div",{staticClass:"task-content"},[t("div",{staticClass:"task-info"},[t("div",{staticClass:"info-item"},[t("div",{staticClass:"label"},[s._v("【备检路段】")]),t("div",{staticClass:"value"},[s._v(s._s(i.checkRoads||"无"))])]),t("div",{staticClass:"info-item"},[t("div",{staticClass:"label"},[s._v("【人行道范围】")]),t("div",{staticClass:"value"},[s._v(s._s(i.walkingRange||"无"))])]),t("div",{staticClass:"info-item"},[t("div",{staticClass:"label"},[s._v("【完成时间】")]),t("div",{staticClass:"value"},[s._v(s._s(i.timeEnd))])]),t("div",{staticClass:"info-item"},[t("div",{staticClass:"label"},[s._v("【检查状态】")]),t("div",{staticClass:"status"},[s._v(s._s(s.getStatus(i.status)))])])])]),t("van-divider",{staticClass:"w-full"}),t("div",{staticClass:"task-footer"},[t("div",{staticClass:"info-item"},[t("span",{staticClass:"label"}),t("span",[s._v(s._s(i.publishTime)+"发布")])]),t("van-button",{attrs:{type:"info",size:"mini",plain:""},on:{click:function(t){return s.getDetail(i)}}},[s._v("查看详情")])],1)],1)})),0)],1)],1)],1)],1)],1)},e=[],o=(a("14d9"),a("e9f5"),a("910d"),a("ab43"),a("a0f3")),n={name:"AssessmentList",data(){return{activeTab:1,loading:!1,finished:!1,refreshing:!1,taskList:[],page:1,pageSize:5,total:0,historyTaskList:[],historyRefreshing:!1,historyLoading:!1,historyFinished:!1,historyPage:1,historyTotal:0,historyPageSize:5}},methods:{getDetail(s){this.$router.push({path:"/assessment/detail",query:{id:s.id}})},onHistoryRefresh(){this.historyRefreshing=!1,this.historyFinished=!0,this.historyTaskList=[],this.historyPage=1,this.onHistoryLoad()},async onHistoryLoad(){try{const s={page:this.historyPage,pageSize:this.historyPageSize,filter:2},t=await Object(o["c"])(s);if(console.log(t),200===t.code){const s=t.rows.map(s=>({...s,assessName:s.assessName,checkRoads:s.roadAssessMinorVOS.filter(s=>1===s.roadType).map(s=>s.roadName).join(","),walkingRange:s.roadAssessMinorVOS.filter(s=>2===s.roadType).map(s=>s.roadName).join(",")}))||[];this.historyTaskList.push(...s),this.historyTotal=t.total||0,this.historyLoading=!1,s.length>=this.historyTotal?(console.log("数据全部加载完成"),this.historyFinished=!0):this.historyPage++}else this.historyLoading=!1,this.$toast.fail(t.msg||"加载失败")}catch(s){this.historyLoading=!1,this.$toast.fail("加载失败")}},getStatus(s){switch(s){case 1:return"待检查";case 2:return"待审查";case 3:return"已审查";case 4:return"正在检查"}},onClickLeft(){this.$router.back()},async onLoad(){try{const s={page:this.page,pageSize:this.pageSize,filter:1},t=await Object(o["c"])(s);if(console.log(t),200===t.code){const s=t.rows.map(s=>({...s,assessName:s.assessName,checkRoads:s.roadAssessMinorVOS.filter(s=>1===s.roadType).map(s=>s.roadName).join(","),walkingRange:s.roadAssessMinorVOS.filter(s=>2===s.roadType).map(s=>s.roadName).join(",")}))||[];this.taskList.push(...s),this.total=t.total||0,this.loading=!1,s.length>=this.total?this.finished=!0:this.page++}else this.loading=!1,this.$toast.fail(t.msg||"加载失败")}catch(s){this.loading=!1,this.$toast.fail("加载失败")}},async onRefresh(){this.finished=!0,this.page=1,this.taskList=[],this.onLoad(),this.refreshing=!1},startTask(s){this.$toast("开始任务："+s.title)},goToRecord(s){this.$router.push({path:"/assessment/record",query:{id:s.id}})}}},r=n,l=(a("c6fa"),a("2877")),c=Object(l["a"])(r,i,e,!1,null,"7a0849ea",null);t["default"]=c.exports},a0f3:function(s,t,a){"use strict";a.d(t,"c",(function(){return e})),a.d(t,"b",(function(){return o})),a.d(t,"d",(function(){return n})),a.d(t,"e",(function(){return r})),a.d(t,"a",(function(){return l}));var i=a("4020");function e(s){return Object(i["a"])({url:"/road/assess/main/list",method:"get",params:s})}function o(s){return Object(i["a"])({url:"/road/assess/main/select",method:"get",params:s})}function n(s){return Object(i["a"])({url:"/road/assess/check/getsubtract",method:"get",params:s})}function r(s){return Object(i["a"])({url:"/road/assess/startcheck/update",method:"put",data:s})}function l(s){return Object(i["a"])({url:"/road/assess/check/add",method:"post",data:s})}},c6fa:function(s,t,a){"use strict";a("d5aa")},d5aa:function(s,t,a){},e5bc:function(s,t){s.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADoAAAA6CAYAAADhu0ooAAAAAXNSR0IArs4c6QAACBBJREFUaEPlW2tsVEUU/mZ3+263bB8EpC1tQQSMCuVNECFAeJRHARP5JRo1YCJEIoKIiIACEaKoPIqKoP7hh1H/aCQUWp4VaAF/yCMChUAobXdLu8W2293tmJl75+7dV+/dtncpsn/27t25M+eb75wzZ86cS6DzQ2tLk2G2FoLQSaBkJECzQJABEIvOLrrYjHoA0gDgGkAvgpIyOJ2HSN5kdk/zQ7RaUHvFYBCyBKBLQUi8Vvuo/k/xAMBP8Lo+Ib3HX+to7LBAaVVpPFKtm0DpWz0OYBAixrZpOxobN5C8ya2hAIcESqvLcxEX8wtAhkWVoS4PRi/C5Z5P+o67GdhVEFDq/OspeNwlAMnq8rgPowNK7yEmZhKxPndVPbwfUM5kbEw5COnzMGTsvjHpHbjcz6uZVYBKNplS/uipa7jpoRfR2DRO2KwPaP35LQB9r/tmtUf0tJ2kjXiXScKB0trTA2GJvRy9NTFKk0BpKyiGk4yRVySgjsr9IHglSsNHdxiKHSR9xApCq0p7wWq9DYLk6EoQpdEYq96mTEIdlS+B4KDeYWm7By13TqHN/je8rgagvR0Waw5Sn3mZd2E/uQGglF9T/i1ds3v8Sr6nvmZ/SP/yC/7dZ8qn/DFH5R601V8DJSaY421I6FuAlNwpIOYYvSKz7hYRWl+xByBL9TzFQDZdOgiP85YkmCyUxdrfB/TER2FA+tprgWQT1HfqNgloxW647l/3m6BY2wBkFCyJACwtJrS+shzAWD1AW24fR8vtE34gGVoJ6GJJsNNb0e5p6TSTDCSxxKPPpE2ShlTs5owKzZC0BEjOn4bUJwv1iM0erWBA6wC2C9H+NJzfDW9rvcKkUMeYVB/QBzcOcdX2qWtkTLLnknImwjpojgT03C603b/mp/bshzkxHX0mfqgttGQu95jquvUuK47yzdwmJaRUNkUKS3I/9Br2ujKoy34Znn9rAMraSjYna7mPadFa2LPcLiYlC/GZQ5W+6s7sgLvxlmzvwtYpQEzImr5DH1BQD2NU9hbazzhOfSw7DFlwbqcUMMUibdRymGK713F7WxtQc2oLqMelOCoxMhs3a+ZX2kLLLSIEyuzGx6Tau5oT0pGY8wJMcdaAwYXXDZTJf36VXzLDDOSDm0fgbqr2NwNJFbkcWTN3GgT05EZ5sZCZDLmERG6T0rKiZ/lRLU8UyJ5lEFD7yY2K6koT2/l1UnjPUCB9fatsUsWkRDpF9qxdxjDacTAQPSaFM8yetdsgoGGDAf0RT3cwKSKo7EIDgQara/SZFCFjTuEeYxitO76+SxGPWFMDHU+gTZoTbNzpeZsd/l5eDjul8JMiZ3axUUDlSIR7SGOYNCXYkDl6GdfOurNfwNNcrwQoSvAvL0HGAtW1DKgmQRZKj3dlTGaMXgZLQjpnytPsQO2ZHfC2OGSz9C1r7EbO7L0GMXpsneFMCpAMgav+OurO7eSRkVBXJaSkFP3nfG0M0Npj6zT3k0k5k3h05Lz6qxLn6rFJNZN6QLI2xgEt+0AVoPs2yny2CUHKwDlIyh7PATZd/wMPqko0Ix5hk5EwKcX/FP3nfmMQo2Vrg7ZLIjNgjrMhfcwKmCxxyuD1F/ah1X5J0YJQ3rUzTEoaQpE791tjgNaUrfUJHSL9EZs2CLZnFys7f+p1w1FZjLaGG0ET1BUmhcfPnWcU0NL3A7ZLwd41sd9YpA5ZqMw024XYz34JTwtbJiSVC/Suem1SMCn6yZ23zyBGS9f43Lx6HQ1YQpIHzEBK/jRFCE9zHWpPb+PeszuYFLFu7rzvDAJ6dE1Qti5c7Jo2/DXEZz6tCNJadxkNV35GxsilyjrZWSZFrJtbZBDQe0fZiYXOYMBkQfqINxFny1fAMptVpym11kmhouooTIzP7uUV7TeG0XtHVyuJMT27EHNcKjLGLPdjUEgWEUjZttUg2XVe0QGDgB5ZLQfZIvGhnRlg2bre41f7LTsRgVSyapImqRPdefMNA7rKl40Lkf4IXCdFIisufTAyCt7gahsRyDBMCsD58783htHqEn4C558J7PCIwbchT8qegMQnRnUYu2rZpJ/qUor8BT8YCTRYXcMxqZyzCPUjhOeFQwXofiA1mBRJYgOBrtSMXdV5V7/UaMCmWWiG4tQC9riBjidwL8r+z1/wo0GMHn7HP5RTMoEdZ+sCVS4sSJ1MigkcsDAioPqPJO4yG233ht/BqAQVKUndIDvwrsGqzs7qCQbotlF2JOGoqNZbhVJzcjO8zexMSuR0Hw6TbPIsSb3Rf8ZnelXXzg6Cz4FgpJ4nnP/8jqaqwyF2MP4ZdBGLhrKrrtikoiWUwjZ0AdKG+jYPGvL/GdlBMNt2nd8LFzuvFM6jK+oaoU2KvFN85hD0nbAKJnOsHn4YC8WRH+173WiqKkHz3XPS1kvYbCgbU+1quswkIbAkZiA5ayxsQ4oiAMldyiIilaem1PX8wkad5AU2YxWgTme2KL/5HARvd7Krnv0YxQGSPuJVCSivycWF/x+r1ANP2xBWy6sqkatkZSArezY9kUpHtpK0gjXsqcev6JGrsFSQfOKRrdUVhLOa3Tb3uJBlrEobVpjsdpfpjZYiVSbj29M7sMRM7bAwWQH7OJSaK2B5oXLqeqB9pd46JOPZCjMCK2wkZCcanesienlA3R2v5TXHrQXwYo+rAGUAQYpB6V5Wk9vRRGu+96JimJW7Tucv+EhvTwwEaK/osU09oLAD5A4IreAv+Hidv5Hek9m7L5qf/wBvWQZP2MqEiwAAAABJRU5ErkJggg=="}}]);