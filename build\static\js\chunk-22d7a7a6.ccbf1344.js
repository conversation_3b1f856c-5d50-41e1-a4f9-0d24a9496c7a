(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-22d7a7a6"],{"180f":function(t,a,e){"use strict";e.r(a);var i=function(){var t=this,a=t._self._c;return a("div",{staticClass:"violation-record"},[a("van-nav-bar",{attrs:{title:"登记违规","left-arrow":"",fixed:""},on:{"click-left":t.onClickLeft}}),a("div",{staticClass:"content"},[a("van-cell-group",[a("van-cell",{attrs:{"title-class":"title-class",title:"路段",value:t.road}}),a("van-cell",{attrs:{"title-class":"title-class",title:"大类",value:t.majorType}}),a("van-cell",{attrs:{"title-class":"title-class",title:"小类",value:t.minorType}})],1),a("van-field",{attrs:{type:"textarea",placeholder:"请输入",label:"问题描述",autosize:{minHeight:100,maxHeight:500}},model:{value:t.description,callback:function(a){t.description=a},expression:"description"}}),a("div",{staticClass:"upload-section"},[a("div",{staticClass:"upload-title"},[t._v("图片选择（最少一张）")]),a("van-uploader",{attrs:{"max-count":9,"after-read":t.afterRead,multiple:""},model:{value:t.fileList,callback:function(a){t.fileList=a},expression:"fileList"}},[a("div",{staticClass:"upload-trigger"},[a("van-icon",{attrs:{name:"photograph",size:"24"}})],1)])],1),a("div",{staticClass:"submit-bar"},[a("van-button",{attrs:{type:"primary",block:""},on:{click:t.handleSave}},[t._v(" 保存 ")])],1)],1)],1)},s=[],l={name:"ViolationRecord",data(){return{road:"",majorType:"",minorType:"",description:"",fileList:[]}},created(){const{road:t,majorType:a,minorType:e}=this.$route.query;this.road=t,this.majorType=a,this.minorType=e},methods:{onClickLeft(){this.$router.back()},afterRead(t){console.log("文件上传:",t)},handleSave(){this.description.trim()?0!==this.fileList.length?(this.$toast.success("保存成功"),this.$router.back()):this.$toast("请至少上传一张图片"):this.$toast("请输入问题描述")}}},o=l,n=(e("27c3"),e("454a"),e("2877")),r=Object(n["a"])(o,i,s,!1,null,"43b7886d",null);a["default"]=r.exports},"27c3":function(t,a,e){"use strict";e("7997")},"454a":function(t,a,e){"use strict";e("a361")},7997:function(t,a,e){},a361:function(t,a,e){}}]);